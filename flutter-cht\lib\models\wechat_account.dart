class WechatAccount {
  final String id;
  final String tenantId;
  final String wxId;
  final String? nickname;
  final String? remark;
  final String? deviceName;
  final String? bigHeadImgUrl;
  final String? smallHeadImgUrl;
  final bool online;

  WechatAccount({
    required this.id,
    required this.tenantId,
    required this.wxId,
    required this.online,
    this.nickname,
    this.remark,
    this.deviceName,
    this.bigHeadImgUrl,
    this.smallHeadImgUrl,
  });

  factory WechatAccount.fromJson(Map<String, dynamic> json) => WechatAccount(
    id: json['id']?.toString() ?? '',
    tenantId:
        json['tenant_id']?.toString() ?? json['tenantId']?.toString() ?? '',
    wxId: json['wx_id']?.toString() ?? json['wxId']?.toString() ?? '',
    nickname: json['nickname'] as String? ?? json['name'] as String?,
    remark: json['remark'] as String?,
    deviceName: json['device'] as String? ?? json['device_name'] as String?,
    bigHeadImgUrl:
        json['big_head_img_url'] as String? ?? json['bigHeadImgUrl'] as String?,
    smallHeadImgUrl:
        json['small_head_img_url'] as String? ??
        json['smallHeadImgUrl'] as String?,
    online: (json['online'] as bool?) ?? (json['isOnline'] as bool?) ?? false,
  );
}
