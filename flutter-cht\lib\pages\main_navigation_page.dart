import 'package:flutter/material.dart';
import 'friends_page.dart';
import 'groups_page.dart';
import 'recent_contacts_page.dart';
import 'settings_page.dart';

/// 主导航页面 - 底部 TabBar 导航
class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  final List<NavigationTab> _tabs = [
    NavigationTab(
      label: '消息',
      icon: Icons.chat,
      activeIcon: Icons.chat,
      page: const RecentContactsPage(),
    ),
    NavigationTab(
      label: '客户',
      icon: Icons.people,
      activeIcon: Icons.people,
      page: const FriendsPage(),
    ),
    NavigationTab(
      label: '群聊',
      icon: Icons.group,
      activeIcon: Icons.group,
      page: const GroupsPage(),
    ),
    NavigationTab(
      label: '设置',
      icon: Icons.settings,
      activeIcon: Icons.settings,
      page: const SettingsPage(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentIndex = _tabController.index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_tabs[_currentIndex].label),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // 搜索按钮
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.pushNamed(context, '/search');
            },
          ),
          // 更多选项
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'sync_friends',
                    child: ListTile(
                      leading: Icon(Icons.sync),
                      title: Text('同步好友'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'notification_test',
                    child: ListTile(
                      leading: Icon(Icons.notifications_active),
                      title: Text('通知测试'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings),
                      title: Text('设置'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'about',
                    child: ListTile(
                      leading: Icon(Icons.info),
                      title: Text('关于'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabs.map((tab) => tab.page).toList(),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          _tabController.animateTo(index);
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items:
            _tabs.map((tab) {
              return BottomNavigationBarItem(
                icon: Icon(tab.icon),
                activeIcon: Icon(tab.activeIcon),
                label: tab.label,
              );
            }).toList(),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'sync_friends':
        _syncFriends();
        break;
      case 'notification_test':
        _openNotificationTest();
        break;
      case 'settings':
        _openSettings();
        break;
      case 'about':
        _showAbout();
        break;
    }
  }

  void _openNotificationTest() {
    Navigator.pushNamed(context, '/notification-test');
  }

  void _syncFriends() {
    // TODO: 实现同步好友功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在同步好友信息...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _openSettings() {
    // TODO: 导航到设置页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('设置功能开发中...'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'IM App',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.chat, size: 48),
      children: [
        const Text('基于 Flutter 开发的即时通讯应用'),
        const SizedBox(height: 16),
        const Text('功能特性：'),
        const Text('• 好友和群聊管理'),
        const Text('• 实时消息收发'),
        const Text('• WebSocket 通信'),
        const Text('• 本地通知'),
      ],
    );
  }
}

/// 导航标签数据类
class NavigationTab {
  final String label;
  final IconData icon;
  final IconData activeIcon;
  final Widget page;

  const NavigationTab({
    required this.label,
    required this.icon,
    required this.activeIcon,
    required this.page,
  });
}
