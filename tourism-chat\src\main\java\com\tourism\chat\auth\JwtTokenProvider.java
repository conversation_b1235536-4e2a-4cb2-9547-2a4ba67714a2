package com.tourism.chat.auth;

import com.tourism.chat.entity.User;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Utility to create and parse JWT tokens.
 */
@Component
public class JwtTokenProvider {

    @Value("${jwt.secret:demoSecretKey123456}")
    private String secret;

    @Value("${jwt.expiration:36000}")
    private long expirationSeconds;

    private SecretKey getKey() {
        // Support plain text secret or base64 encoded; pad/convert as needed
        byte[] keyBytes = secret.length() < 32 ? (secret + "00000000000000000000000000000000").substring(0, 32).getBytes() : secret.getBytes();
        try {
            // Try base64 decode first
            byte[] decoded = Decoders.BASE64.decode(secret);
            if (decoded.length >= 32) {
                keyBytes = decoded;
            }
        } catch (Exception ignored) {
        }
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String createToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", user.getName());
        claims.put("phone", user.getPhone());
        claims.put("id", user.getId());
        Long tenantId = user.getTenantId();
        if (tenantId != null) {
            claims.put("tenantId", tenantId);
        }
        Date now = new Date();
        Date expiry = new Date(now.getTime() + expirationSeconds * 1000);
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiry)
                .signWith(getKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    public boolean validateToken(String token) {
        try {
            parseClaims(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public Claims parseClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public String getUsername(String token) {
        return parseClaims(token).getSubject();
    }

    public Long getTenantId(String token) {
        Object v = parseClaims(token).get("tenantId");
        if (v == null) return null;
        if (v instanceof Number n) return n.longValue();
        return Long.parseLong(v.toString());
    }
}

