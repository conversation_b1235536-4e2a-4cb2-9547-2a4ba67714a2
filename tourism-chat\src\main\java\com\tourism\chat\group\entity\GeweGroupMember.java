package com.tourism.chat.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.tourism.chat.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("gewe_group_member")
public class GeweGroupMember extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("chatroom_id")
    private String chatroomId;

    @TableField("wxid")
    private String wxid;

    @TableField("nick_name")
    private String nickName;

    @TableField("display_name")
    private String displayName;

    @TableField("inviter_user_name")
    private String inviterUserName;

    @TableField("member_flag")
    private Integer memberFlag; // 群主/管理员/普通成员 标记


    @TableField("small_head_img_url")
    private String smallHeadImgUrl;

}

