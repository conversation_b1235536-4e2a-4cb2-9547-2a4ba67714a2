import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TokenStorage {
  static const _kTokenKey = 'auth_token';
  static String? _cachedToken;

  static Future<SharedPreferences?> _prefs() async {
    try {
      return await SharedPreferences.getInstance();
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ SharedPreferences not available yet: $e');
      }
      return null; // fallback to memory cache
    }
  }

  static Future<void> saveToken(String token) async {
    _cachedToken = token;
    final sp = await _prefs();
    await sp?.setString(_kTokenKey, token);
  }

  static Future<String?> getToken() async {
    if (_cachedToken != null && _cachedToken!.isNotEmpty) return _cachedToken;
    final sp = await _prefs();
    _cachedToken = sp?.getString(_kTokenKey);
    return _cachedToken;
  }

  static Future<void> clearToken() async {
    _cachedToken = null;
    final sp = await _prefs();
    await sp?.remove(_kTokenKey);
  }
}
