package com.tourism.chat.wx.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.group.mapper.GeweGroupMessageMapper;
import com.tourism.chat.wx.model.WeChatMessage;
import com.tourism.chat.wx.service.GeweGroupMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 群聊消息服务实现类
 */
@Service
@Slf4j
public class GeweGroupMessageServiceImpl extends ServiceImpl<GeweGroupMessageMapper, GeweGroupMessage> implements GeweGroupMessageService {
    @Override
    public Page<GeweGroupMessage> findGroupChatHistory(String groupWxId, Pageable pageable) {
        Page<GeweGroupMessage> page = this.lambdaQuery()
                .eq(GeweGroupMessage::getChatroomId, groupWxId)
                .orderByDesc(GeweGroupMessage::getCreateTime)
                .page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        return page;
    }

    @Override
    public void saveSentMessage(String fromWxId, String toWxId, String content, int i, boolean b, String atWxIds, WeChatMessage message) {
        GeweGroupMessage gm = new GeweGroupMessage();
        gm.setChatroomId(toWxId);
        gm.setSenderWxid(fromWxId);
        gm.setContent(fromWxId + ":" + content);
        gm.setMessageType(String.valueOf(i));
        gm.setAtWxid(atWxIds);
        gm.setCreateTime(LocalDateTime.now());
        this.save(gm);
    }
}
