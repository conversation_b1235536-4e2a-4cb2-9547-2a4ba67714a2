package com.tourism.chat.tenant;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Reads X-Tenant-ID from headers and sets TenantContext for the request lifetime.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TenantFilter implements Filter {

    public static final String TENANT_HEADER = "X-Tenant-ID";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String tenantHeader = httpRequest.getHeader(TENANT_HEADER);
        try {
            if (tenantHeader != null && !tenantHeader.isBlank()) {
                try {
                    // TenantContext.setTenantId(Long.parseLong(tenantHeader));
                } catch (NumberFormatException e) {
                    httpResponse.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid X-Tenant-ID header");
                    return;
                }
            }
            chain.doFilter(request, response);
        } finally {
            TenantContextHolder.clear();
        }
    }
    @Override
    public void destroy() {
        TenantContextHolder.clear();
    }


}

