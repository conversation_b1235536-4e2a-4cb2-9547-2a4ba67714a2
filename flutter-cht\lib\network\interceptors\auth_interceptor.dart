import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 认证拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加通用请求头
    options.headers['X-Requested-With'] = 'XMLHttpRequest';
    options.headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8';

    // 添加时间戳
    options.headers['X-Timestamp'] =
        DateTime.now().millisecondsSinceEpoch.toString();

    // 添加设备信息
    options.headers['X-Platform'] = defaultTargetPlatform.name;

    // Authorization 由 DioClient.setAuthToken 统一设置，这里不再强制覆盖。

    if (kDebugMode) {
      print('🔐 Auth Interceptor - Request: ${options.method} ${options.uri}');
      print('🔐 Headers: ${options.headers}');
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('🔐 Auth Interceptor - Response: ${response.statusCode}');
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('🔐 Auth Interceptor - Error: ${err.message}');
    }

    // 处理认证相关错误
    if (err.response?.statusCode == 401) {
      // Token 过期或无效，可以在这里处理自动刷新 token 或跳转到登录页
      if (kDebugMode) {
        print('🔐 Authentication failed - Token expired or invalid');
      }
      // TODO: 实现 token 刷新逻辑或触发登录事件
    }

    super.onError(err, handler);
  }
}
