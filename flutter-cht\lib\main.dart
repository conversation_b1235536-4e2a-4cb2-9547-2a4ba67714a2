import 'package:flutter/material.dart';
import 'core/app_initializer.dart';
import 'config/app_config.dart';
import 'pages/main_with_accounts_page.dart';
import 'pages/startup_page.dart';
import 'pages/login_page.dart';

import 'pages/search_page.dart';
import 'pages/friend_detail_page.dart';
import 'pages/group_detail_page.dart';
import 'pages/group_members_page.dart';
import 'pages/group_member_detail_page.dart';
import 'pages/chat_page.dart';
import 'pages/notification_test_page.dart';
import 'models/gewe_friend.dart';
import 'services/message_notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化应用
  await AppInitializer.initialize();

  WidgetsFlutterBinding.ensureInitialized();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // 全局导航器键
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    // 设置通知服务的导航器键
    MessageNotificationService.setNavigatorKey(navigatorKey);
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: AppConfig.appName,
      debugShowCheckedModeBanner: !AppConfig.isDebug,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
      ),
      home: const StartupPage(),
      routes: {
        '/login': (context) => const LoginPage(),
        '/main': (context) => const MainWithAccountsPage(),
        '/search': (context) => const SearchPage(),
        '/notification-test': (context) => const NotificationTestPage(),
        '/friend-detail': (context) {
          final friend =
              ModalRoute.of(context)!.settings.arguments as GeWeFriend;
          return FriendDetailPage(friend: friend);
        },
        '/group-detail': (context) {
          final group =
              ModalRoute.of(context)!.settings.arguments as GeWeFriend;
          return GroupDetailPage(group: group);
        },
        '/group-members': (context) {
          final args =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return GroupMembersPage(
            chatroomId: args['chatroomId'] as String,
            title: args['title'] as String?,
          );
        },
        '/group-member-detail': (context) {
          final args =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return GroupMemberDetailPage(
            chatroomId: args['chatroomId'] as String,
            memberWxid: args['memberWxid'] as String,
          );
        },
        '/chat': (context) {
          final args =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return ChatPage(
            contactWxId: args['contactWxId'],
            contactName: args['contactName'],
            isGroup: args['isGroup'] ?? false,
          );
        },
      },
      // routes: {'/gewe-demo': (context) => const GeWeDemoPage()},
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  String _status = 'Ready';
  bool _isLoading = false;

  void _testNetworkConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing network connection...';
    });

    try {
      // 这里可以测试网络连接
      await Future.delayed(const Duration(seconds: 2));
      setState(() {
        _status =
            'Network configuration completed!\n\n'
            'Base URL: ${AppConfig.baseUrl}\n'
            'WebSocket URL: ${AppConfig.websocketUrl}\n'
            'App Version: ${AppConfig.appVersion}';
      });
    } catch (e) {
      setState(() {
        _status = 'Network test failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(Icons.network_check, size: 64, color: Colors.blue),
            const SizedBox(height: 24),
            const Text(
              'IM App Network Configuration',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.left,
              ),
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const CircularProgressIndicator()
            else
              Column(
                children: [
                  ElevatedButton.icon(
                    onPressed: _testNetworkConnection,
                    icon: const Icon(Icons.network_check),
                    label: const Text('Test Network Configuration'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                  // const SizedBox(height: 16),
                  // ElevatedButton.icon(
                  //   onPressed: () {
                  //     Navigator.pushNamed(context, '/gewe-demo');
                  //   },
                  //   icon: const Icon(Icons.chat),
                  //   label: const Text('GeWe Demo'),
                  //   style: ElevatedButton.styleFrom(
                  //     padding: const EdgeInsets.symmetric(
                  //       horizontal: 24,
                  //       vertical: 12,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
