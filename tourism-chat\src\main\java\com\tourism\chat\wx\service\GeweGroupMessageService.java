package com.tourism.chat.wx.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.model.WeChatMessage;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * GeWe消息发送服务接口
 */
public interface GeweGroupMessageService extends IService<GeweGroupMessage> {


    Page<GeweGroupMessage> findGroupChatHistory(String groupWxId, Pageable pageable);

    void saveSentMessage(String fromWxId, String toWxId,String content, int i, boolean b, String atWxIds, WeChatMessage message);
}