/// 应用全局配置
class AppConfig {
  // 后端 API Base URL
  // WebSocket 地址（Spring WebSocket 端点）
  // 例如 ws://<host>:8080/api/ws
  // static const String baseUrl = 'http://*************:8080/api';
  // static const String websocketUrl = 'ws://*************:8080/api/ws';
  //
  // static const String baseUrl = 'http://**************:8080/api';
  // static const String websocketUrl = 'ws://**************:8080/api/ws';
  // //
  static const String baseUrl = 'http://*************:18080/api';
  static const String websocketUrl = 'ws://*************:18080/api/ws';

  // API 超时时间
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // 应用信息
  static const String appName = 'IM App';
  static const String appVersion = '1.0.0';

  // 调试模式
  static const bool isDebug = true;

  // 文件上传相关
  static const String fileUploadEndpoint = '/file/upload';
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB

  // 头像相关路径
  static const String userIconPath = '/file/user-icon/';
  static const String groupIconPath = '/file/group-icon/';
  static const String chatImagePath = '/file/chat-space/';

  // 推送相关
  static const String fcmServerKey = 'your-fcm-server-key';

  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
}
