# Redis消息订阅者使用说明

## 概述

本项目实现了基于Redis的分布式消息系统，支持群聊消息、私聊消息和系统消息的分发。通过专门的订阅者类来处理不同类型的消息，提高了系统的可维护性和扩展性。

## 架构设计

### 消息订阅者

1. **GroupMessageSubscriber** - 群聊消息订阅者
   - 频道：`socket:group:messages`
   - 处理群聊消息的分发

2. **PrivateMessageSubscriber** - 私聊消息订阅者
   - 频道：`socket:private:messages`
   - 处理私聊消息的分发

3. **SystemMessageSubscriber** - 系统消息订阅者
   - 频道：`socket:system:messages`
   - 处理系统通知（用户上线/下线、强制断开、广播等）

### 消息发布器

**MessagePublisher** - 统一的消息发布工具类
- 提供便捷的方法发布各种类型的消息到对应的Redis频道

## 使用方法

### 1. 发布群聊消息

```java
@Autowired
private MessagePublisher messagePublisher;

// 创建群聊消息
ChatMessage groupMessage = new ChatMessage();
groupMessage.setTenantId(1L);
groupMessage.setGroupId("group123");
groupMessage.setFromUserId("user456");
groupMessage.setContent("Hello, group!");

// 发布消息
messagePublisher.publishGroupMessage(groupMessage);
```

### 2. 发布私聊消息

```java
// 创建私聊消息
ChatMessage privateMessage = new ChatMessage();
privateMessage.setTenantId(1L);
privateMessage.setFromUserId("user123");
privateMessage.setToUserId("user456");
privateMessage.setContent("Hello, private!");

// 发布消息
messagePublisher.publishPrivateMessage(privateMessage);
```

### 3. 发布系统消息

```java
// 用户上线通知
messagePublisher.publishUserOnline(1L, "user123");

// 用户下线通知
messagePublisher.publishUserOffline(1L, "user123");

// 强制断开连接
messagePublisher.publishForceDisconnect(1L, "user123", "违规操作");

// 租户级广播
messagePublisher.publishTenantBroadcast(1L, "系统维护通知", null);

// 全局广播
messagePublisher.publishGlobalBroadcast("系统升级通知", null);
```

### 4. 自定义系统消息

```java
SystemMessageSubscriber.SystemMessage customMessage = new SystemMessageSubscriber.SystemMessage();
customMessage.setType("CUSTOM_EVENT");
customMessage.setTenantId(1L);
customMessage.setUserId("user123");
customMessage.setContent("自定义事件");
customMessage.setTimestamp(System.currentTimeMillis());

messagePublisher.publishSystemMessage(customMessage);
```

## 房间命名规范

### 用户房间
- 格式：`user:{tenantId}:{userId}`
- 示例：`user:1:user123`
- 用途：私聊消息、个人通知

### 群聊房间
- 格式：`group:{tenantId}:{groupId}`
- 示例：`group:1:group456`
- 用途：群聊消息

### 租户房间
- 格式：`tenant:{tenantId}`
- 示例：`tenant:1`
- 用途：租户级广播、用户上线/下线通知

### 微信房间
- 格式：`wx:{tenantId}:{wxId}`
- 示例：`wx:1:wxid_abc123`
- 用途：微信相关消息

## Redis频道说明

| 频道名称 | 用途 | 订阅者 |
|---------|------|--------|
| `socket:group:messages` | 群聊消息 | GroupMessageSubscriber |
| `socket:private:messages` | 私聊消息 | PrivateMessageSubscriber |
| `socket:system:messages` | 系统消息 | SystemMessageSubscriber |

## 系统消息类型

| 类型 | 说明 | 处理方式 |
|------|------|----------|
| `USER_ONLINE` | 用户上线 | 向租户房间广播 |
| `USER_OFFLINE` | 用户下线 | 向租户房间广播 |
| `FORCE_DISCONNECT` | 强制断开 | 向用户房间发送并断开连接 |
| `BROADCAST` | 广播消息 | 根据tenantId决定范围 |

## 配置说明

### Redis配置

```java
@Configuration
public class RedisConfig {
    @Bean
    public RedisMessageListenerContainer redisContainer(
            RedisConnectionFactory connectionFactory,
            GroupMessageSubscriber groupSubscriber,
            PrivateMessageSubscriber privateSubscriber,
            SystemMessageSubscriber systemSubscriber) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 注册订阅者
        container.addMessageListener(groupSubscriber, new ChannelTopic("socket:group:messages"));
        container.addMessageListener(privateSubscriber, new ChannelTopic("socket:private:messages"));
        container.addMessageListener(systemSubscriber, new ChannelTopic("socket:system:messages"));
        
        return container;
    }
}
```

## 最佳实践

1. **消息格式统一**：使用JSON格式序列化消息
2. **错误处理**：每个订阅者都包含异常处理逻辑
3. **日志记录**：详细记录消息处理过程
4. **性能优化**：避免在订阅者中执行耗时操作
5. **扩展性**：可以轻松添加新的消息类型和订阅者

## 监控和调试

### 日志级别设置

```yaml
logging:
  level:
    com.tourism.chat.socket: DEBUG
```

### Redis监控

```bash
# 监控Redis频道活动
redis-cli monitor

# 查看订阅者状态
redis-cli pubsub channels socket:*
```

## 注意事项

1. 确保Redis服务正常运行
2. 消息订阅者是单例Bean，注意线程安全
3. 大量消息时考虑性能影响
4. 定期清理过期的在线状态数据
5. 在集群环境中测试消息分发的正确性
