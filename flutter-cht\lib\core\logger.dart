import 'package:flutter/foundation.dart';

/// 日志级别枚举
enum LogLevel {
  verbose,
  debug,
  info,
  warning,
  error,
}

/// 日志工具类
class Logger {
  static const String _tag = 'IMApp';

  /// 是否启用日志输出
  static bool get enabled => kDebugMode;

  /// 打印 verbose 级别日志
  static void v(String message, [Object? object]) {
    if (enabled) {
      _printLog(LogLevel.verbose, message, object);
    }
  }

  /// 打印 debug 级别日志
  static void d(String message, [Object? object]) {
    if (enabled) {
      _printLog(LogLevel.debug, message, object);
    }
  }

  /// 打印 info 级别日志
  static void i(String message, [Object? object]) {
    if (enabled) {
      _printLog(LogLevel.info, message, object);
    }
  }

  /// 打印 warning 级别日志
  static void w(String message, [Object? object]) {
    if (enabled) {
      _printLog(LogLevel.warning, message, object);
    }
  }

  /// 打印 error 级别日志
  static void e(String message, [Object? object]) {
    if (enabled) {
      _printLog(LogLevel.error, message, object);
    }
  }

  /// 实际打印日志的方法
  static void _printLog(LogLevel level, String message, [Object? object]) {
    final levelStr = level.toString().split('.').last.toUpperCase();
    final timestamp = DateTime.now().toString().split('.').first;
    final buffer = StringBuffer();

    buffer.write('[$_tag] [$levelStr] [$timestamp] $message');
    
    if (object != null) {
      buffer.write(': $object');
    }

    // 使用 Flutter 的 print 方式输出日志
    // 在 release 模式下会被忽略
    print(buffer.toString());
  }
}