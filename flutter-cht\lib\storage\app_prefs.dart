import 'package:shared_preferences/shared_preferences.dart';

class AppPrefs {
  static const _kSelectedWxId = 'selected_wx_id';

  static Future<void> setSelectedWxId(String wxId) async {
    final sp = await SharedPreferences.getInstance();
    await sp.setString(_kSelectedWxId, wxId);
  }

  static Future<String?> getSelectedWxId() async {
    final sp = await SharedPreferences.getInstance();
    return sp.getString(_kSelectedWxId);
  }
}

