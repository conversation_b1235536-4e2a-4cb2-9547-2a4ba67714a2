import 'package:flutter/material.dart';
import '../storage/token_storage.dart';
import '../network/dio_client.dart';

/// 启动路由页：根据是否存在 Token 跳转到登录或主页面
class StartupPage extends StatefulWidget {
  const StartupPage({super.key});

  @override
  State<StartupPage> createState() => _StartupPageState();
}

class _StartupPageState extends State<StartupPage> {
  @override
  void initState() {
    super.initState();
    _route();
  }

  Future<void> _route() async {
    final token = await _loadTokenWithRetry();
    if (!mounted) return;
    if (token != null && token.isNotEmpty) {
      DioClient.instance.setAuthToken(token);
      Navigator.of(context).pushReplacementNamed('/main');
    } else {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  Future<String?> _loadTokenWithRetry({int maxTries = 5}) async {
    String? token;
    for (int i = 0; i < maxTries; i++) {
      token = await TokenStorage.getToken();
      if (token != null && token.isNotEmpty) return token;
      await Future.delayed(const Duration(milliseconds: 200));
    }
    return token;
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}
