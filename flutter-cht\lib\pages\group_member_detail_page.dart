import 'package:flutter/material.dart';
import '../services/gewe_group_service.dart';

class GroupMemberDetailPage extends StatefulWidget {
  final String chatroomId;
  final String memberWxid;
  const GroupMemberDetailPage({super.key, required this.chatroomId, required this.memberWxid});

  @override
  State<GroupMemberDetailPage> createState() => _GroupMemberDetailPageState();
}

class _GroupMemberDetailPageState extends State<GroupMemberDetailPage> {
  final GeWeGroupService _groupService = GeWeGroupService();
  bool _loading = true;
  String? _error;
  MemberDetail? _detail;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() {
      _loading = true;
      _error = null;
    });
    final resp = await _groupService.getChatroomMemberDetail(widget.chatroomId, [widget.memberWxid]);
    if (!mounted) return;
    if (resp.isSuccess && (resp.data?.isNotEmpty ?? false)) {
      setState(() {
        _detail = resp.data!.first;
        _loading = false;
      });
    } else {
      setState(() {
        _error = resp.message;
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('成员详情'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_loading) return const Center(child: CircularProgressIndicator());
    if (_error != null) return Center(child: Text(_error!));
    final d = _detail!;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.green[200],
            backgroundImage: (d.smallHeadImgUrl != null && d.smallHeadImgUrl!.isNotEmpty)
                ? NetworkImage(d.smallHeadImgUrl!)
                : null,
            child: (d.smallHeadImgUrl == null || d.smallHeadImgUrl!.isEmpty)
                ? const Icon(Icons.person, color: Colors.white, size: 40)
                : null,
          ),
          const SizedBox(height: 12),
          Text(d.nickName ?? d.userName, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          if (d.alias?.isNotEmpty == true)
            Text('@${d.alias}', style: const TextStyle(color: Colors.grey)),
          const SizedBox(height: 16),
          _infoRow('微信ID', d.userName),
          if (d.country != null) _infoRow('国家', d.country!),
          if (d.province != null) _infoRow('省份', d.province!),
          if (d.city != null) _infoRow('城市', d.city!),
          if (d.signature != null) _infoRow('签名', d.signature!),
          if (d.memberFlag != null) _infoRow('成员标记', d.memberFlag.toString()),
        ],
      ),
    );
  }

  Widget _infoRow(String label, String value) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(color: Colors.grey.withOpacity(0.1), blurRadius: 3, offset: const Offset(0, 1)),
        ],
      ),
      child: Row(
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

