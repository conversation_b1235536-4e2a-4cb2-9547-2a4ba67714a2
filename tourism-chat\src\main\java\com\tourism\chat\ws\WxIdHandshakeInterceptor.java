package com.tourism.chat.ws;

import java.util.Map;

import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

/**
 * 在握手阶段从 URL 参数中提取 wxId/chatroomId，构建 sessionKey 放入 attributes。
 * sessionKey = chatroomId 存在时：wxId|chatroomId，否则为 wxId。
 */
@Component
public class WxIdHandshakeInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
            WebSocketHandler wsHandler, Map<String, Object> attributes) {
        String query = request.getURI().getQuery();
        String wxId = getQueryParam(query, "wxId");
        if (wxId == null || wxId.isBlank()) {
            return false; // 缺少 wxId，拒绝握手
        }
        String chatroomId = getQueryParam(query, "chatroomId");
        String sessionKey = (chatroomId != null && !chatroomId.isBlank()) ? (wxId + "|" + chatroomId) : wxId;
        attributes.put("sessionKey", sessionKey);
        attributes.put("wxId", wxId);
        if (chatroomId != null && !chatroomId.isBlank()) {
            attributes.put("chatroomId", chatroomId);
        }
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
            WebSocketHandler wsHandler, @Nullable Exception exception) {
        // no-op
    }

    @Nullable
    private String getQueryParam(@Nullable String query, String key) {
        if (query == null || query.isEmpty())
            return null;
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf('=');
            if (idx > 0) {
                String k = pair.substring(0, idx);
                if (key.equals(k)) {
                    return idx + 1 < pair.length() ? decode(pair.substring(idx + 1)) : "";
                }
            }
        }
        return null;
    }

    private String decode(String v) {
        try {
            return java.net.URLDecoder.decode(v, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return v;
        }
    }
}
