import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/message_callback_test_service.dart';
import '../services/message_notification_service.dart';

/// 通知测试页面
class NotificationTestPage extends StatefulWidget {
  const NotificationTestPage({super.key});

  @override
  State<NotificationTestPage> createState() => _NotificationTestPageState();
}

class _NotificationTestPageState extends State<NotificationTestPage> {
  final MessageCallbackTestService _testService =
      MessageCallbackTestService.instance;
  final MessageNotificationService _notificationService =
      MessageNotificationService.instance;

  bool _isLoading = false;
  String _lastTestResult = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('消息通知测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildTestButtons(),
            const SizedBox(height: 16),
            _buildResultCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  '测试说明',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '此页面用于测试消息通知功能：\n'
              '• 点击测试按钮模拟接收新消息\n'
              '• 验证通知是否正常显示\n'
              '• 测试点击通知跳转功能\n'
              '• 支持前台和后台通知',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.play_arrow, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  '测试功能',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 单个消息测试
            _buildTestButton(
              title: '测试文本消息',
              subtitle: '模拟接收一条文本消息',
              icon: Icons.message,
              onPressed: _testTextMessage,
            ),
            const SizedBox(height: 8),

            _buildTestButton(
              title: '测试图片消息',
              subtitle: '模拟接收一条图片消息',
              icon: Icons.image,
              onPressed: _testImageMessage,
            ),
            const SizedBox(height: 8),

            _buildTestButton(
              title: '测试群聊消息',
              subtitle: '模拟接收一条群聊消息',
              icon: Icons.group,
              onPressed: _testGroupMessage,
            ),
            const SizedBox(height: 8),

            _buildTestButton(
              title: '测试语音消息',
              subtitle: '模拟接收一条语音消息',
              icon: Icons.mic,
              onPressed: _testVoiceMessage,
            ),
            const SizedBox(height: 16),

            // 批量测试
            _buildTestButton(
              title: '运行完整测试场景',
              subtitle: '依次测试多种消息类型',
              icon: Icons.playlist_play,
              onPressed: _runTestScenario,
              isPrimary: true,
            ),
            const SizedBox(height: 16),

            // 清除通知
            _buildTestButton(
              title: '清除所有通知',
              subtitle: '清除当前显示的所有通知',
              icon: Icons.clear_all,
              onPressed: _clearAllNotifications,
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onPressed,
    bool isPrimary = false,
    Color? color,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isPrimary ? Colors.blue : (color ?? Colors.grey[100]),
          foregroundColor:
              isPrimary
                  ? Colors.white
                  : (color != null ? Colors.white : Colors.black87),
          padding: const EdgeInsets.all(16),
        ),
        child: Row(
          children: [
            Icon(icon),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: isPrimary ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (_isLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    if (_lastTestResult.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  '测试结果',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _lastTestResult,
                style: const TextStyle(fontSize: 14, fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testTextMessage() async {
    await _runTest('文本消息', () async {
      await _testService.testTextMessage(
        fromWxId: 'test_friend_001',
        fromName: '测试好友',
        toWxId: 'current_user',
        content: '你好！这是一条测试文本消息 📱',
      );
    });
  }

  Future<void> _testImageMessage() async {
    await _runTest('图片消息', () async {
      await _testService.testImageMessage(
        fromWxId: 'test_friend_002',
        fromName: '摄影师朋友',
        toWxId: 'current_user',
      );
    });
  }

  Future<void> _testGroupMessage() async {
    await _runTest('群聊消息', () async {
      await _testService.testGroupMessage(
        fromWxId: 'test_friend_003',
        fromName: '群友小明',
        groupWxId: 'test_group_001',
        content: '大家好！这是一条群聊测试消息 👥',
      );
    });
  }

  Future<void> _testVoiceMessage() async {
    await _runTest('语音消息', () async {
      await _testService.testVoiceMessage(
        fromWxId: 'test_friend_004',
        fromName: '话痨朋友',
        toWxId: 'current_user',
      );
    });
  }

  Future<void> _runTestScenario() async {
    await _runTest('完整测试场景', () async {
      await _testService.runTestScenario();
    });
  }

  Future<void> _clearAllNotifications() async {
    await _runTest('清除通知', () async {
      await _notificationService.clearAllNotifications();
    });
  }

  Future<void> _runTest(
    String testName,
    Future<void> Function() testFunction,
  ) async {
    setState(() {
      _isLoading = true;
      _lastTestResult = '正在执行 $testName 测试...';
    });

    try {
      await testFunction();
      setState(() {
        _lastTestResult =
            '✅ $testName 测试成功\n'
            '时间: ${DateTime.now().toString().substring(0, 19)}\n'
            '状态: 已发送测试请求，请检查通知显示';
      });

      if (kDebugMode) {
        print('✅ $testName test completed');
      }
    } catch (e) {
      setState(() {
        _lastTestResult =
            '❌ $testName 测试失败\n'
            '时间: ${DateTime.now().toString().substring(0, 19)}\n'
            '错误: $e';
      });

      if (kDebugMode) {
        print('❌ $testName test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
