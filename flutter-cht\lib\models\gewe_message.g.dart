// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gewe_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeWeMessage _$GeWeMessageFromJson(Map<String, dynamic> json) => GeWeMessage(
  id: json['id'] as String?,
  msgId: json['messageId'] as String?,
  fromWxId: json['fromWxId'] as String?,
  toWxId: json['toWxId'] as String?,
  content: json['content'] as String?,
  msgType: (json['messageType'] as num?)?.toInt(),
  createTime:
      json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
  sendTime: GeWeMessage._dateTimeFromJson(json['sendTime']),
  receiveTime: GeWeMessage._dateTimeFromJson(json['receiveTime']),
  isGroup: json['isGroup'] as bool?,
  isSent: json['isSent'] as bool?,
  atWxIds: json['atWxIds'] as String?,
  pushContent: json['pushContent'] as String?,
);

Map<String, dynamic> _$GeWeMessageToJson(GeWeMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'messageId': instance.msgId,
      'fromWxId': instance.fromWxId,
      'toWxId': instance.toWxId,
      'content': instance.content,
      'messageType': instance.msgType,
      'createTime': instance.createTime?.toIso8601String(),
      'sendTime': instance.sendTime?.toIso8601String(),
      'receiveTime': instance.receiveTime?.toIso8601String(),
      'isGroup': instance.isGroup,
      'isSent': instance.isSent,
      'atWxIds': instance.atWxIds,
      'pushContent': instance.pushContent,
    };

SendMessageRequest _$SendMessageRequestFromJson(Map<String, dynamic> json) =>
    SendMessageRequest(
      toWxId: json['toWxId'] as String,
      content: json['content'] as String,
      messageType: json['messageType'] as String? ?? 'friend',
      atWxIds: json['atWxIds'] as String?,
    );

Map<String, dynamic> _$SendMessageRequestToJson(SendMessageRequest instance) =>
    <String, dynamic>{
      'toWxId': instance.toWxId,
      'content': instance.content,
      'messageType': instance.messageType,
      'atWxIds': instance.atWxIds,
    };

SendMessageResponse _$SendMessageResponseFromJson(Map<String, dynamic> json) =>
    SendMessageResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
    );

Map<String, dynamic> _$SendMessageResponseToJson(
  SendMessageResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
};

RecentContactsResponse _$RecentContactsResponseFromJson(
  Map<String, dynamic> json,
) => RecentContactsResponse(
  recentChats:
      (json['recentChats'] as List<dynamic>).map((e) => e as String).toList(),
  recentGroups:
      (json['recentGroups'] as List<dynamic>).map((e) => e as String).toList(),
);

Map<String, dynamic> _$RecentContactsResponseToJson(
  RecentContactsResponse instance,
) => <String, dynamic>{
  'recentChats': instance.recentChats,
  'recentGroups': instance.recentGroups,
};
