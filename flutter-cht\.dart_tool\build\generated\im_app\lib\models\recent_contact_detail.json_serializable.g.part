// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecentContactDetail _$RecentContactDetailFromJson(Map<String, dynamic> json) =>
    RecentContactDetail(
      contact: GeWeFriend.fromJson(json['contact'] as Map<String, dynamic>),
      lastMessage:
          json['lastMessage'] == null
              ? null
              : GeWeMessage.fromJson(
                json['lastMessage'] as Map<String, dynamic>,
              ),
      lastMessageTime: DateTime.parse(json['lastMessageTime'] as String),
      unreadCount: (json['unreadCount'] as num).toInt(),
    );

Map<String, dynamic> _$RecentContactDetailToJson(
  RecentContactDetail instance,
) => <String, dynamic>{
  'contact': instance.contact,
  'lastMessage': instance.lastMessage,
  'lastMessageTime': instance.lastMessageTime.toIso8601String(),
  'unreadCount': instance.unreadCount,
};
