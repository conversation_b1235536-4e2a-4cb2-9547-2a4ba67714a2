import 'package:json_annotation/json_annotation.dart';
import 'gewe_friend.dart';
import 'gewe_message.dart';

part 'recent_contact_detail.g.dart';

@JsonSerializable()
class RecentContactDetail {
  final GeWeFriend contact;
  final GeWeMessage? lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;

  const RecentContactDetail({
    required this.contact,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.unreadCount,
  });

  factory RecentContactDetail.fromJson(Map<String, dynamic> json) {
    GeWeFriend contact;
    if (json['contact'] is Map<String, dynamic>) {
      contact = GeWeFriend.fromJson(json['contact'] as Map<String, dynamic>);
    } else {
      // 兼容扁平结构
      contact = GeWeFriend(
        wxId: (json['contactWxId'] ?? json['wxId'] ?? '') as String,
        nickName: json['nickName'] as String?,
        remark: json['remark'] as String?,
        smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
        bigHeadImgUrl: json['bigHeadImgUrl'] as String?,
        isGroup: json['isGroup'] as bool?,
        displayName: json['displayName'] as String?,
      );
    }

    GeWeMessage? lastMsg;
    if (json['lastMessage'] is Map<String, dynamic>) {
      lastMsg = GeWeMessage.fromJson(json['lastMessage'] as Map<String, dynamic>);
    }

    DateTime? ts;
    final v = json['lastMessageTime'];
    if (v is String) {
      try { ts = DateTime.parse(v); } catch (_) {}
    } else if (v is num) {
      final millis = v > 1e12 ? v.toInt() : (v.toInt() * 1000);
      ts = DateTime.fromMillisecondsSinceEpoch(millis);
    }
    ts ??= lastMsg?.createTime ?? lastMsg?.sendTime ?? DateTime.now();

    final unread = (json['unreadCount'] as num?)?.toInt() ?? 0;

    return RecentContactDetail(
      contact: contact,
      lastMessage: lastMsg,
      lastMessageTime: ts,
      unreadCount: unread,
    );
  }

  factory RecentContactDetail.fromJsonGenerated(Map<String, dynamic> json) =>
      _$RecentContactDetailFromJson(json);

  Map<String, dynamic> toJson() => _$RecentContactDetailToJson(this);
}

