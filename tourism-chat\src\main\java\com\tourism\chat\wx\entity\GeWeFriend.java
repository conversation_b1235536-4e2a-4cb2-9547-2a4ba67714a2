package com.tourism.chat.wx.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tourism.chat.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

/**
 * GeWe微信好友信息实体
 */
@TableName("gewe_friend")
@Getter
@Setter
public class GeWeFriend extends BaseEntity {

    @Id
    private Long id;

    private String masterWxid;

    /**
     * 微信ID
     */
    @TableField(value = "wx_id")
    private String wxId;

    /**
     * 昵称
     */
    @TableField(value = "nick_name")
    private String nickName;

    /**
     * 拼音首字母
     */
    @TableField(value = "py_initial")
    private String pyInitial;

    /**
     * 全拼
     */
    @TableField(value = "quan_pin")
    private String quanPin;

    /**
     * 性别 1-男 2-女
     */
    @TableField(value = "sex")
    private Integer sex;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 备注拼音首字母
     */
    @TableField(value = "remark_py_initial")
    private String remarkPyInitial;

    /**
     * 备注全拼
     */
    @TableField(value = "remark_quan_pin")
    private String remarkQuanPin;

    /**
     * 个性签名
     */
    @TableField(value = "signature")
    private String signature;

    /**
     * 微信号
     */
    @TableField(value = "alias")
    private String alias;

    /**
     * 朋友圈背景图
     */
    @TableField(value = "sns_bg_img")
    private String snsBgImg;

    /**
     * 国家
     */
    @TableField(value = "country")
    private String country;

    /**
     * 省份
     */
    @TableField(value = "province")
    private String province;

    /**
     * 城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 大头像URL
     */
    @TableField(value = "big_head_img_url")
    private String bigHeadImgUrl;

    /**
     * 小头像URL
     */
    @TableField(value = "small_head_img_url")
    private String smallHeadImgUrl;

    /**
     * 是否为群聊
     */
    @TableField(value = "is_group")
    private Boolean isGroup = false;

    // /**
    //  * 创建时间
    //  */
    // @TableField(value = "create_time")
    // private Date createTime;
    //
    // /**
    //  * 更新时间
    //  */
    // @TableField(value = "update_time")
    // private Date updateTime;

    /**
     * 是否已同步
     */
    @TableField(value = "synced")
    private Boolean synced = true;
}