POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 57
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.3.5
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "phone": "123",
  "password": "",
  "tenantId": 100
}

<> 2025-09-01T190724.200.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 55
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.3.5
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "phone": "123",
  "password": "",
  "tenantId": 0
}

<> 2025-09-01T190717.200.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 55
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.3.5
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "phone": "123",
  "password": "",
  "tenantId": 0
}

<> 2025-09-01T190649.500.json

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 55
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2024.3.5
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*

{
  "phone": "123",
  "password": "",
  "tenantId": 0
}

<> 2025-09-01T190307.500.json

###

