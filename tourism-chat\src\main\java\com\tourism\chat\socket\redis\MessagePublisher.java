package com.tourism.chat.socket.redis;

import com.alibaba.fastjson2.JSON;
import com.tourism.chat.constants.RedisKeyConstants;
import com.tourism.chat.socket.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 消息发布工具类
 * 用于向不同的Redis频道发布消息
 */
@Component
@Slf4j
public class MessagePublisher {

    private final RedisTemplate<String, Object> redisTemplate;

    public MessagePublisher(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 发布群聊消息
     */
    public void publishGroupMessage(ChatMessage message) {
        try {
            if (message.getGroupId() == null) {
                log.warn("群聊消息的groupId不能为空: {}", message);
                return;
            }
            
            String jsonMessage = JSON.toJSONString(message);
            redisTemplate.convertAndSend(RedisKeyConstants.Channel.GROUP_MESSAGES, jsonMessage);
            
            log.info("📤 群聊消息已发布到Redis: 群组={}, 发送者={}", 
                    message.getGroupId(), message.getFromUserId());
                    
        } catch (Exception e) {
            log.error("发布群聊消息失败", e);
        }
    }

    /**
     * 发布私聊消息
     */
    public void publishPrivateMessage(ChatMessage message) {
        try {
            if (message.getToUserId() == null) {
                log.warn("私聊消息的toUserId不能为空: {}", message);
                return;
            }
            
            String jsonMessage = JSON.toJSONString(message);
            redisTemplate.convertAndSend(RedisKeyConstants.Channel.PRIVATE_MESSAGES, jsonMessage);
            
            log.info("📤 私聊消息已发布到Redis: 发送者={}, 接收者={}", 
                    message.getFromUserId(), message.getToUserId());
                    
        } catch (Exception e) {
            log.error("发布私聊消息失败", e);
        }
    }

    /**
     * 发布系统消息
     */
    public void publishSystemMessage(SystemMessageSubscriber.SystemMessage message) {
        try {
            String jsonMessage = JSON.toJSONString(message);
            redisTemplate.convertAndSend(RedisKeyConstants.Channel.SYSTEM_MESSAGES, jsonMessage);
            
            log.info("📤 系统消息已发布到Redis: 类型={}, 租户={}, 用户={}", 
                    message.getType(), message.getTenantId(), message.getUserId());
                    
        } catch (Exception e) {
            log.error("发布系统消息失败", e);
        }
    }

    /**
     * 发布用户上线通知
     */
    public void publishUserOnline(String tenantId, String userId) {
        SystemMessageSubscriber.SystemMessage message = new SystemMessageSubscriber.SystemMessage();
        message.setType(RedisKeyConstants.SystemMessageType.USER_ONLINE);
        message.setTenantId(tenantId);
        message.setUserId(userId);
        message.setTimestamp(System.currentTimeMillis());
        message.setContent("用户上线");
        
        publishSystemMessage(message);
    }

    /**
     * 发布用户下线通知
     */
    public void publishUserOffline(String tenantId, String userId) {
        SystemMessageSubscriber.SystemMessage message = new SystemMessageSubscriber.SystemMessage();
        message.setType(RedisKeyConstants.SystemMessageType.USER_OFFLINE);
        message.setTenantId(tenantId);
        message.setUserId(userId);
        message.setTimestamp(System.currentTimeMillis());
        message.setContent("用户下线");
        
        publishSystemMessage(message);
    }

    /**
     * 发布强制断开连接通知
     */
    public void publishForceDisconnect(String tenantId, String userId, String reason) {
        SystemMessageSubscriber.SystemMessage message = new SystemMessageSubscriber.SystemMessage();
        message.setType(RedisKeyConstants.SystemMessageType.FORCE_DISCONNECT);
        message.setTenantId(tenantId);
        message.setUserId(userId);
        message.setTimestamp(System.currentTimeMillis());
        message.setContent(reason != null ? reason : "强制下线");
        
        publishSystemMessage(message);
    }

    /**
     * 发布租户级广播消息
     */
    public void publishTenantBroadcast(String tenantId, String content, Object data) {
        SystemMessageSubscriber.SystemMessage message = new SystemMessageSubscriber.SystemMessage();
        message.setType(RedisKeyConstants.SystemMessageType.BROADCAST);
        message.setTenantId(tenantId);
        message.setTimestamp(System.currentTimeMillis());
        message.setContent(content);
        message.setData(data);
        
        publishSystemMessage(message);
    }

    /**
     * 发布全局广播消息
     */
    public void publishGlobalBroadcast(String content, Object data) {
        SystemMessageSubscriber.SystemMessage message = new SystemMessageSubscriber.SystemMessage();
        message.setType(RedisKeyConstants.SystemMessageType.BROADCAST);
        message.setTenantId(null); // null表示全局广播
        message.setTimestamp(System.currentTimeMillis());
        message.setContent(content);
        message.setData(data);
        
        publishSystemMessage(message);
    }

    /**
     * 通用消息发布方法
     */
    public void publishToChannel(String channel, Object message) {
        try {
            String jsonMessage = message instanceof String ? 
                    (String) message : JSON.toJSONString(message);
            redisTemplate.convertAndSend(channel, jsonMessage);
            
            log.info("📤 消息已发布到频道 [{}]: {}", channel, 
                    jsonMessage.length() > 100 ? jsonMessage.substring(0, 100) + "..." : jsonMessage);
                    
        } catch (Exception e) {
            log.error("发布消息到频道 [{}] 失败", channel, e);
        }
    }
}
