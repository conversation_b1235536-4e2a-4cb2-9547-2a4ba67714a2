import 'package:json_annotation/json_annotation.dart';

part 'gewe_message.g.dart';

/// 消息类型枚举
enum MessageType {
  @JsonValue(1)
  text(1, '文本消息'),
  @JsonValue(3)
  image(3, '图片消息'),
  @JsonValue(34)
  voice(34, '语音消息'),
  @JsonValue(43)
  video(43, '视频消息'),
  @JsonValue(47)
  emoji(47, '表情消息'),
  @JsonValue(48)
  location(48, '位置消息'),
  @JsonValue(49)
  file(49, '文件消息'),
  @JsonValue(10000)
  system(10000, '系统消息');

  const MessageType(this.value, this.description);

  final int value;
  final String description;

  static MessageType fromValue(int? value) {
    if (value == null) return MessageType.text;
    return MessageType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MessageType.text,
    );
  }
}

/// GeWe 消息模型
@JsonSerializable()
class GeWeMessage {
  final String? id;

  @JsonKey(name: 'messageId')
  final String? msgId;

  @JsonKey(name: 'fromWxId')
  final String? fromWxId;

  @JsonKey(name: 'toWxId')
  final String? toWxId;

  @JsonKey(name: 'content')
  final String? content;

  @JsonKey(name: 'messageType')
  final int? msgType;

  @JsonKey(name: 'createTime')
  final DateTime? createTime;

  @JsonKey(name: 'sendTime', fromJson: _dateTimeFromJson)
  final DateTime? sendTime;

  @JsonKey(name: 'receiveTime', fromJson: _dateTimeFromJson)
  final DateTime? receiveTime;

  @JsonKey(name: 'isGroup')
  final bool? isGroup;

  @JsonKey(name: 'isSent')
  final bool? isSent;

  @JsonKey(name: 'atWxIds')
  final String? atWxIds;

  @JsonKey(name: 'pushContent')
  final String? pushContent;

  const GeWeMessage({
    this.id,
    this.msgId,
    this.fromWxId,
    this.toWxId,
    this.content,
    this.msgType,
    this.createTime,
    this.sendTime,
    this.receiveTime,
    this.isGroup,
    this.isSent,
    this.atWxIds,
    this.pushContent,
    // this.sendTime,
  });

  /// 获取消息类型
  MessageType get messageType => MessageType.fromValue(msgType);

  // /// 获取创建时间（DateTime）
  DateTime? get createDateTime => createTime;

  /// 是否为群聊消息
  bool get isGroupMessage => isGroup == true;

  /// 是否为发送的消息
  bool get isSentMessage => isSent == true;

  /// 获取@的用户列表
  List<String> get atUserList {
    if (atWxIds == null || atWxIds!.isEmpty) return [];
    return atWxIds!.split(',').where((id) => id.isNotEmpty).toList();
  }

  factory GeWeMessage.fromJson(Map<String, dynamic> json) {
    // 兼容不同后端字段命名
    final String? from = (json['fromWxId'] ?? json['senderWxid']) as String?;
    final String? to = (json['toWxId'] ?? json['chatroomId']) as String?;

    // messageType 既可能是数字也可能是字符串
    final dynamic mtRaw = json['messageType'] ?? json['msgType'];
    int? mt;
    if (mtRaw is num) {
      mt = mtRaw.toInt();
    } else if (mtRaw is String) {
      mt = int.tryParse(mtRaw);
    }

    // atWxIds 兼容 atWxid
    final String? at = (json['atWxIds'] ?? json['atWxid']) as String?;

    // isGroup 缺省时根据 to 判断
    bool? isGroup = json['isGroup'] as bool?;
    if (isGroup == null && to != null) {
      isGroup = to.endsWith('@chatroom') || to.startsWith('@@');
    }

    return GeWeMessage(
      id: json['id']?.toString(),
      msgId: json['messageId']?.toString(),
      fromWxId: from,
      toWxId: to,
      content: json['content'] as String?,
      msgType: mt,
      createTime: _dateTimeFromJson(json['createTime']),
      sendTime:
          _dateTimeFromJson(json['sendTime']) ??
          _dateTimeFromJson(json['createTime']),
      receiveTime:
          _dateTimeFromJson(json['receiveTime']) ??
          _dateTimeFromJson(json['createTime']),
      isGroup: isGroup,
      // 后端无 isSent，direction=1 视为我方发送
      isSent: (json['direction'] as num?)?.toInt() == 1,
      atWxIds: at,
      pushContent: json['pushContent'] as String?,
    );
  }

  Map<String, dynamic> toJson() => _$GeWeMessageToJson(this);

  /// 数组转 DateTime
  static int? _timestampFromArray(List<dynamic>? arr) {
    if (arr == null || arr.length < 6) return null;
    return DateTime(
          arr[0],
          arr[1],
          arr[2],
          arr[3],
          arr[4],
          arr[5],
        ).millisecondsSinceEpoch ~/
        1000; // 秒级时间戳
  }

  static DateTime? _dateTimeFromJson(dynamic value) {
    if (value == null) return null;

    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (_) {
        // 兼容 "yyyy-MM-dd HH:mm:ss" 格式
        try {
          final parts = value.split(' ');
          if (parts.length == 2) {
            final d = parts[0].split('-').map(int.parse).toList();
            final t = parts[1].split(':').map(int.parse).toList();
            if (d.length == 3 && t.length >= 2) {
              final year = d[0], month = d[1], day = d[2];
              final hour = t[0], minute = t[1];
              final second = t.length >= 3 ? t[2] : 0;
              return DateTime(year, month, day, hour, minute, second);
            }
          }
        } catch (_) {}
        return null;
      }
    }

    return null;
  }

  @override
  String toString() {
    return 'GeWeMessage{msgId: $msgId, from: $fromWxId, to: $toWxId, type: ${messageType.description}, content: $content}';
  }
}

/// 发送消息请求
@JsonSerializable()
class SendMessageRequest {
  @JsonKey(name: 'toWxId')
  final String toWxId;

  @JsonKey(name: 'content')
  final String content;

  @JsonKey(name: 'messageType')
  final String messageType;

  @JsonKey(name: 'atWxIds')
  final String? atWxIds;

  const SendMessageRequest({
    required this.toWxId,
    required this.content,
    this.messageType = 'friend',
    this.atWxIds,
  });

  factory SendMessageRequest.friend({
    required String toWxId,
    required String content,
  }) {
    return SendMessageRequest(
      toWxId: toWxId,
      content: content,
      messageType: 'friend',
    );
  }

  factory SendMessageRequest.group({
    required String toWxId,
    required String content,
    String? atWxIds,
  }) {
    return SendMessageRequest(
      toWxId: toWxId,
      content: content,
      messageType: 'group',
      atWxIds: atWxIds,
    );
  }

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SendMessageRequestToJson(this);
}

/// 发送消息响应
@JsonSerializable()
class SendMessageResponse {
  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String message;

  const SendMessageResponse({required this.success, required this.message});

  factory SendMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$SendMessageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SendMessageResponseToJson(this);
}

/// 最近联系人响应
@JsonSerializable()
class RecentContactsResponse {
  @JsonKey(name: 'recentChats')
  final List<String> recentChats;

  @JsonKey(name: 'recentGroups')
  final List<String> recentGroups;

  const RecentContactsResponse({
    required this.recentChats,
    required this.recentGroups,
  });

  factory RecentContactsResponse.fromJson(Map<String, dynamic> json) =>
      _$RecentContactsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RecentContactsResponseToJson(this);
}
