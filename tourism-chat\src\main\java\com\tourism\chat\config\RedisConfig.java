package com.tourism.chat.config;

import com.tourism.chat.constants.RedisKeyConstants;
import com.tourism.chat.socket.redis.GroupMessageSubscriber;
import com.tourism.chat.socket.redis.PrivateMessageSubscriber;
import com.tourism.chat.socket.redis.SystemMessageSubscriber;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis configuration: RedisTemplate with String key and JSON value.
 */
@Configuration
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        template.afterPropertiesSet();
        return template;
    }

    /**
     * Redis消息监听容器 - 使用专门的订阅者
     */
    @Bean
    public RedisMessageListenerContainer redisContainer(
            RedisConnectionFactory connectionFactory,
            GroupMessageSubscriber groupSubscriber,
            PrivateMessageSubscriber privateSubscriber,
            SystemMessageSubscriber systemSubscriber) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);

        // 群聊消息订阅者
        container.addMessageListener(groupSubscriber, new ChannelTopic(RedisKeyConstants.Channel.GROUP_MESSAGES));

        // 私聊消息订阅者
        container.addMessageListener(privateSubscriber, new ChannelTopic(RedisKeyConstants.Channel.PRIVATE_MESSAGES));

        // 系统消息订阅者
        container.addMessageListener(systemSubscriber, new ChannelTopic(RedisKeyConstants.Channel.SYSTEM_MESSAGES));

        return container;
    }

    /**
     * 备用配置：如果需要保留原有的通用订阅者，可以取消注释
     * 这样可以同时支持新旧两种方式
     */
    /*
    @Bean
    public RedisMessageListenerContainer legacyRedisContainer(
            RedisConnectionFactory connectionFactory,
            RedisMessageSubscriber subscriber) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(subscriber, new ChannelTopic("socket:legacy:messages"));
        return container;
    }
    */
}

