import 'dart:async';
import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';
import '../models/gewe_message.dart';
import '../services/gewe_message_service.dart';
import '../services/gewe_service_manager.dart';
import '../widgets/recent_contact_item.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_widget.dart';

/// 最近联系人页面
class RecentContactsPage extends StatefulWidget {
  const RecentContactsPage({super.key});

  @override
  State<RecentContactsPage> createState() => _RecentContactsPageState();
}

class _RecentContactsPageState extends State<RecentContactsPage>
    with AutomaticKeepAliveClientMixin {
  final GeWeMessageService _messageService = GeWeMessageService();
  final ScrollController _scrollController = ScrollController();

  List<RecentContact> _recentContacts = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  // 当前用户微信ID（实际应用中应该从用户状态管理中获取）
  String? _currentUserWxId;

  @override
  bool get wantKeepAlive => true;

  StreamSubscription<String?>? _accountSub;

  @override
  void initState() {
    super.initState();
    _loadRecentContacts();

    _accountSub = GeWeServiceManager.instance.accountStream.listen((_) {
      if (mounted) _loadRecentContacts();
    });
  }

  @override
  void dispose() {
    _accountSub?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载最近联系人
  Future<void> _loadRecentContacts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // 从全局服务获取当前用户选择的微信ID
      _currentUserWxId = GeWeServiceManager.instance.currentUserWxId;
      if (_currentUserWxId == null || _currentUserWxId!.isEmpty) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = '请先在左侧选择一个账号';
        });
        return;
      }

      // 调用“详情”接口，直接返回最后消息、时间与未读数
      final response = await _messageService.getRecentContactsDetail(
        wxId: _currentUserWxId!,
        limit: 50,
      );

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          final details = response.data!;
          final contacts =
              details
                  .map(
                    (d) => RecentContact(
                      contact: d.contact,
                      lastMessage: d.lastMessage,
                      lastMessageTime: d.lastMessageTime,
                      unreadCount: d.unreadCount,
                    ),
                  )
                  .toList()
                ..sort(
                  (a, b) => b.lastMessageTime.compareTo(a.lastMessageTime),
                );

          setState(() {
            _recentContacts = contacts;
            _isLoading = false;
          });
        } else {
          setState(() {
            _hasError = true;
            _errorMessage = response.message;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '加载最近联系人失败: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新最近联系人
  Future<void> _refreshRecentContacts() async {
    await _loadRecentContacts();
  }

  /// 开始聊天
  void _startChat(RecentContact contact) async {
    // 进入聊天前清零未读并调用后端置已读
    final mgr = GeWeServiceManager.instance;
    final me = mgr.currentUserWxId;
    final wx = contact.contact.wxId;
    if (me != null && wx.isNotEmpty) {
      mgr.clearUnreadFor(wx);
      if (contact.contact.isGroupChat) {
        await GeWeMessageService().markGroupRead(groupWxId: wx);
      } else {
        await GeWeMessageService().markFriendConversationRead(
          wxId: me,
          contactWxId: wx,
        );
      }
    }

    if (!mounted) return;
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': contact.contact.wxId,
        'contactName': contact.contact.name,
        'isGroup': contact.contact.isGroupChat,
      },
    );
  }

  /// 删除最近联系人
  void _deleteRecentContact(RecentContact contact) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('删除联系人'),
            content: Text('确定要从最近联系人中删除 ${contact.contact.name} 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  setState(() {
                    _recentContacts.remove(contact);
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('已删除 ${contact.contact.name}')),
                  );
                },
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: StreamBuilder<Map<String, int>>(
        stream: GeWeServiceManager.instance.unreadStream,
        builder: (context, snapshot) {
          // 将流里的未读合并进展示
          final unread = GeWeServiceManager.instance.unreadSnapshot;
          final merged =
              _recentContacts
                  .map(
                    (c) => RecentContact(
                      contact: c.contact,
                      lastMessage: c.lastMessage,
                      lastMessageTime: c.lastMessageTime,
                      unreadCount: unread[c.contact.wxId] ?? c.unreadCount,
                    ),
                  )
                  .toList();

          return RefreshIndicator(
            onRefresh: _refreshRecentContacts,
            child: _buildBody(overrideList: merged),
          );
        },
      ),
    );
  }

  Widget _buildBody({List<RecentContact>? overrideList}) {
    final list = overrideList ?? _recentContacts;

    if (_isLoading && list.isEmpty) {
      return const LoadingWidget(message: '正在加载最近联系人...');
    }

    if (_hasError && list.isEmpty) {
      return EmptyWidget(
        icon: Icons.error_outline,
        title: '加载失败',
        subtitle: _errorMessage,
        actionText: '重试',
        onAction: _loadRecentContacts,
      );
    }

    if (list.isEmpty) {
      return const EmptyWidget(
        icon: Icons.chat_outlined,
        title: '暂无最近联系人',
        subtitle: '开始聊天后会显示在这里',
      );
    }

    return _buildRecentContactsListView(list);
  }

  Widget _buildRecentContactsListView(List<RecentContact> list) {
    return ListView.separated(
      controller: _scrollController,
      itemCount: list.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final contact = list[index];
        return RecentContactItem(
          recentContact: contact,
          onTap: () => _startChat(contact),
          onDelete: () => _deleteRecentContact(contact),
        );
      },
    );
  }
}

/// 最近联系人数据模型
class RecentContact {
  final GeWeFriend contact;
  final GeWeMessage? lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;

  const RecentContact({
    required this.contact,
    this.lastMessage,
    required this.lastMessageTime,
    required this.unreadCount,
  });
}
