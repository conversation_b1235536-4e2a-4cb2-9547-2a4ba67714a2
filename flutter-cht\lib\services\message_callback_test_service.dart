import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gewe_message_callback.dart';
import '../models/gewe_message.dart';
import '../network/base_service.dart';

/// 消息回调测试服务
class MessageCallbackTestService extends BaseService {
  static MessageCallbackTestService? _instance;

  static MessageCallbackTestService get instance {
    _instance ??= MessageCallbackTestService._();
    return _instance!;
  }

  MessageCallbackTestService._();

  /// 测试消息回调
  /// 调用后端的 /message/callback/test 接口模拟新消息
  Future<void> testMessageCallback({
    required String fromWxId,
    required String toWxId,
    required String content,
    bool isGroup = false,
    MessageType messageType = MessageType.text,
  }) async {
    try {
      // 构造回调数据
      final callbackData = GeWeMessageCallback(
        typeName: 'MessageCallback',
        appid: 'test_app',
        wxid: toWxId,
        data: null, // 简化测试数据
      );

      if (kDebugMode) {
        print('🧪 Testing message callback: ${callbackData.toJson()}');
      }

      // 调用测试接口
      final response = await post<Map<String, dynamic>>(
        '/message/callback/test',
        data: callbackData.toJson(),
        fromJson: (json) => json,
      );

      if (response.isSuccess) {
        if (kDebugMode) {
          print('✅ Message callback test successful');
        }
      } else {
        if (kDebugMode) {
          print('❌ Message callback test failed: ${response.message}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Message callback test error: $e');
      }
      rethrow;
    }
  }

  /// 测试文本消息
  Future<void> testTextMessage({
    required String fromWxId,
    required String fromName,
    required String toWxId,
    required String content,
    bool isGroup = false,
  }) async {
    await testMessageCallback(
      fromWxId: fromWxId,
      toWxId: toWxId,
      content: content,
      isGroup: isGroup,
      messageType: MessageType.text,
    );
  }

  /// 测试图片消息
  Future<void> testImageMessage({
    required String fromWxId,
    required String fromName,
    required String toWxId,
    bool isGroup = false,
  }) async {
    await testMessageCallback(
      fromWxId: fromWxId,
      toWxId: toWxId,
      content: '[图片]',
      isGroup: isGroup,
      messageType: MessageType.image,
    );
  }

  /// 测试语音消息
  Future<void> testVoiceMessage({
    required String fromWxId,
    required String fromName,
    required String toWxId,
    bool isGroup = false,
  }) async {
    await testMessageCallback(
      fromWxId: fromWxId,
      toWxId: toWxId,
      content: '[语音]',
      isGroup: isGroup,
      messageType: MessageType.voice,
    );
  }

  /// 测试群聊消息
  Future<void> testGroupMessage({
    required String fromWxId,
    required String fromName,
    required String groupWxId,
    required String content,
  }) async {
    await testMessageCallback(
      fromWxId: fromWxId,
      toWxId: groupWxId,
      content: content,
      isGroup: true,
      messageType: MessageType.text,
    );
  }

  /// 批量测试多种消息类型
  Future<void> testMultipleMessages({
    required String fromWxId,
    required String fromName,
    required String toWxId,
    bool isGroup = false,
  }) async {
    final messages = [
      {'content': '你好！这是一条测试消息', 'type': MessageType.text},
      {'content': '[图片]', 'type': MessageType.image},
      {'content': '[语音]', 'type': MessageType.voice},
      {'content': '再见！', 'type': MessageType.text},
    ];

    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];

      // 延迟发送，模拟真实场景
      await Future.delayed(Duration(seconds: i + 1));

      await testMessageCallback(
        fromWxId: fromWxId,
        toWxId: toWxId,
        content: message['content'] as String,
        isGroup: isGroup,
        messageType: message['type'] as MessageType,
      );

      if (kDebugMode) {
        print('📤 Sent test message ${i + 1}/${messages.length}');
      }
    }
  }

  /// 预设测试场景
  Future<void> runTestScenario() async {
    if (kDebugMode) {
      print('🎬 Running message notification test scenario...');
    }

    try {
      // 场景1: 好友私聊消息
      await testTextMessage(
        fromWxId: 'test_friend_001',
        fromName: '测试好友',
        toWxId: 'current_user',
        content: '你好！这是一条来自好友的测试消息',
      );

      await Future.delayed(const Duration(seconds: 2));

      // 场景2: 群聊消息
      await testGroupMessage(
        fromWxId: 'test_friend_002',
        fromName: '群友小明',
        groupWxId: 'test_group_001',
        content: '大家好！这是一条群聊测试消息',
      );

      await Future.delayed(const Duration(seconds: 2));

      // 场景3: 图片消息
      await testImageMessage(
        fromWxId: 'test_friend_003',
        fromName: '摄影师朋友',
        toWxId: 'current_user',
      );

      await Future.delayed(const Duration(seconds: 2));

      // 场景4: 语音消息
      await testVoiceMessage(
        fromWxId: 'test_friend_004',
        fromName: '话痨朋友',
        toWxId: 'current_user',
      );

      if (kDebugMode) {
        print('✅ Test scenario completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Test scenario failed: $e');
      }
    }
  }
}
