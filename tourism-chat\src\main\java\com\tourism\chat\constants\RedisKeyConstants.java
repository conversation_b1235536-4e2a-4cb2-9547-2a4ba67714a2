package com.tourism.chat.constants;

/**
 * Redis Key 和 Room Key 常量定义
 * 统一管理所有的前缀和键名规范
 */
public final class RedisKeyConstants {

    private RedisKeyConstants() {
        // 工具类，禁止实例化
    }

    // ==================== Redis Key 前缀 ====================

    /**
     * 在线状态相关前缀
     */
    public static final class Online {
        /** 用户在线状态: online:user:{tenantId}:{userId} */
        public static final String USER_PREFIX = "online:user:";
        
        /** 微信号在线状态: online:wx:{tenantId}:{wxId} */
        public static final String WX_PREFIX = "online:wx:";
        
        /**
         * 构建用户在线状态键
         * @param tenantId 租户ID
         * @param userId 用户ID
         * @return online:user:{tenantId}:{userId}
         */
        public static String userKey(String tenantId, String userId) {
            return USER_PREFIX + tenantId + ":" + userId;
        }
        
        /**
         * 构建微信号在线状态键
         * @param tenantId 租户ID
         * @param wxId 微信ID
         * @return online:wx:{tenantId}:{wxId}
         */
        public static String wxKey(String tenantId, String wxId) {
            return WX_PREFIX + tenantId + ":" + wxId;
        }
    }

    /**
     * 会话映射相关前缀
     */
    public static final class Session {
        /** Socket会话映射: session:socket:{socketId} */
        public static final String SOCKET_PREFIX = "session:socket:";
        
        /** 用户会话映射: session:user:{tenantId}:{userId} */
        public static final String USER_PREFIX = "session:user:";
        
        /** 微信会话映射: session:wx:{tenantId}:{wxId} */
        public static final String WX_PREFIX = "session:wx:";
        
        /**
         * 构建Socket会话键
         * @param socketId Socket ID
         * @return session:socket:{socketId}
         */
        public static String socketKey(String socketId) {
            return SOCKET_PREFIX + socketId;
        }
        
        /**
         * 构建用户会话键
         * @param tenantId 租户ID
         * @param userId 用户ID
         * @return session:user:{tenantId}:{userId}
         */
        public static String userKey(Long tenantId, String userId) {
            return USER_PREFIX + tenantId + ":" + userId;
        }
        
        /**
         * 构建微信会话键
         * @param tenantId 租户ID
         * @param wxId 微信ID
         * @return session:wx:{tenantId}:{wxId}
         */
        public static String wxKey(Long tenantId, String wxId) {
            return WX_PREFIX + tenantId + ":" + wxId;
        }
    }

    /**
     * 群聊信息相关前缀
     */
    public static final class Group {
        /** 群基本信息: group:info:{tenantId}:{groupId} */
        public static final String INFO_PREFIX = "group:info:";
        
        /** 群成员列表: group:members:{tenantId}:{groupId} */
        public static final String MEMBERS_PREFIX = "group:members:";
        
        /**
         * 构建群信息键
         * @param tenantId 租户ID
         * @param groupId 群组ID
         * @return group:info:{tenantId}:{groupId}
         */
        public static String infoKey(Long tenantId, String groupId) {
            return INFO_PREFIX + tenantId + ":" + groupId;
        }
        
        /**
         * 构建群成员键
         * @param tenantId 租户ID
         * @param groupId 群组ID
         * @return group:members:{tenantId}:{groupId}
         */
        public static String membersKey(Long tenantId, String groupId) {
            return MEMBERS_PREFIX + tenantId + ":" + groupId;
        }
    }

    /**
     * 消息相关前缀
     */
    public static final class Message {
        /** 用户离线消息: msg:offline:{tenantId}:{userId} */
        public static final String OFFLINE_USER_PREFIX = "msg:offline:";
        
        /** 微信离线消息: msg:offline:wx:{tenantId}:{wxId} */
        public static final String OFFLINE_WX_PREFIX = "msg:offline:wx:";
        
        /**
         * 构建用户离线消息键
         * @param tenantId 租户ID
         * @param userId 用户ID
         * @return msg:offline:{tenantId}:{userId}
         */
        public static String offlineUserKey(Long tenantId, String userId) {
            return OFFLINE_USER_PREFIX + tenantId + ":" + userId;
        }
        
        /**
         * 构建微信离线消息键
         * @param tenantId 租户ID
         * @param wxId 微信ID
         * @return msg:offline:wx:{tenantId}:{wxId}
         */
        public static String offlineWxKey(Long tenantId, String wxId) {
            return OFFLINE_WX_PREFIX + tenantId + ":" + wxId;
        }
    }

    // ==================== Redis Pub/Sub 频道 ====================

    /**
     * Redis 发布订阅频道
     */
    public static final class Channel {
        /** 群聊消息频道 */
        public static final String GROUP_MESSAGES = "socket:group:messages";
        
        /** 私聊消息频道 */
        public static final String PRIVATE_MESSAGES = "socket:private:messages";
        
        /** 系统消息频道 */
        public static final String SYSTEM_MESSAGES = "socket:system:messages";
        
        /** 跨节点消息广播频道 */
        public static final String SOCKET_MESSAGES = "channel:socket:messages";
        
        /** 系统通知频道 */
        public static final String SOCKET_SYSTEM = "channel:socket:system";
    }

    // ==================== SocketIO Room 前缀 ====================

    /**
     * SocketIO 房间前缀
     */
    public static final class Room {
        /** 用户房间前缀: user:{tenantId}:{userId} */
        public static final String USER_PREFIX = "user:";
        
        /** 群聊房间前缀: group:{tenantId}:{groupId} */
        public static final String GROUP_PREFIX = "group:";
        
        /** 租户房间前缀: tenant:{tenantId} */
        public static final String TENANT_PREFIX = "tenant:";
        
        /** 微信房间前缀: wx:{tenantId}:{wxId} */
        public static final String WX_PREFIX = "wx:";
        
        /**
         * 构建用户房间名
         * @param tenantId 租户ID
         * @param userId 用户ID
         * @return user:{tenantId}:{userId}
         */
        public static String userRoom(String tenantId, String userId) {
            return USER_PREFIX + tenantId + ":" + userId;
        }
        
        /**
         * 构建群聊房间名
         * @param tenantId 租户ID
         * @param groupId 群组ID
         * @return group:{tenantId}:{groupId}
         */
        public static String groupRoom(String tenantId, String groupId) {
            return GROUP_PREFIX + tenantId + ":" + groupId;
        }
        
        /**
         * 构建租户房间名
         * @param tenantId 租户ID
         * @return tenant:{tenantId}
         */
        public static String tenantRoom(String tenantId) {
            return TENANT_PREFIX + tenantId;
        }
        
        /**
         * 构建微信房间名
         * @param tenantId 租户ID
         * @param wxId 微信ID
         * @return wx:{tenantId}:{wxId}
         */
        public static String wxRoom(String tenantId, String wxId) {
            return WX_PREFIX + tenantId + ":" + wxId;
        }
    }

    // ==================== 系统消息类型 ====================

    /**
     * 系统消息类型常量
     */
    public static final class SystemMessageType {
        /** 用户上线 */
        public static final String USER_ONLINE = "USER_ONLINE";
        
        /** 用户下线 */
        public static final String USER_OFFLINE = "USER_OFFLINE";
        
        /** 强制断开连接 */
        public static final String FORCE_DISCONNECT = "FORCE_DISCONNECT";
        
        /** 广播消息 */
        public static final String BROADCAST = "BROADCAST";
        
        /** 自定义事件 */
        public static final String CUSTOM_EVENT = "CUSTOM_EVENT";
    }

    // ==================== SocketIO 事件名称 ====================

    /**
     * SocketIO 事件名称常量
     */
    public static final class Event {
        /** 群聊消息事件 */
        public static final String GROUP_MESSAGE = "group_message";
        
        /** 私聊消息事件 */
        public static final String PRIVATE_MESSAGE = "private_message";
        
        /** 用户上线事件 */
        public static final String USER_ONLINE = "user_online";
        
        /** 用户下线事件 */
        public static final String USER_OFFLINE = "user_offline";
        
        /** 强制断开事件 */
        public static final String FORCE_DISCONNECT = "force_disconnect";
        
        /** 系统广播事件 */
        public static final String SYSTEM_BROADCAST = "system_broadcast";
        
        /** 全局广播事件 */
        public static final String GLOBAL_BROADCAST = "global_broadcast";
    }

    // ==================== WebSocket Session Key 分隔符 ====================

    /**
     * WebSocket 会话键相关常量
     */
    public static final class WebSocketSession {
        /** 群聊会话键分隔符: wxId|chatroomId */
        public static final String CHATROOM_SEPARATOR = "|";
    }
}
