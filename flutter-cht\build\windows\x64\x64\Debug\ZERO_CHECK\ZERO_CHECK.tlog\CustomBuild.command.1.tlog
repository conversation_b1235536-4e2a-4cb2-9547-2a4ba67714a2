^E:\ME\FLUTTER-CHT\BUILD\WINDOWS\X64\CMAKEFILES\C59394804C5BCFC69762EA0B6AA94F1D\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/me/flutter-cht/windows -BE:/me/flutter-cht/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/me/flutter-cht/build/windows/x64/im_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
