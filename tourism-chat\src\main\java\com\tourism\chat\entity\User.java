package com.tourism.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * User entity for basic authentication and multi-tenant demo.
 */
@Data
@TableName("user")
public class User extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** username */
    private String name;

    private String phone ;

    /** password (BCrypt recommended) */
    private String password;

    @TableField("tenant_id")
    private Long tenantId;
}

