# IM App - Flutter 即时通讯应用

## 项目概述

这是一个基于 Flutter 开发的即时通讯应用，集成了完整的网络层架构、WebSocket 实时通信和本地通知功能。

## 技术栈

- **Flutter**: 跨平台移动应用开发框架
- **Dart**: 编程语言
- **dio**: HTTP 网络请求库
- **json_serializable**: JSON 序列化/反序列化
- **web_socket_channel**: WebSocket 客户端
- **flutter_local_notifications**: 本地推送通知

## 项目结构

```
lib/
├── config/                 # 配置文件
│   └── app_config.dart     # 应用全局配置
├── core/                   # 核心功能
│   └── app_initializer.dart # 应用初始化器
├── models/                 # 数据模型
│   ├── api_response.dart   # API 响应模型
│   └── api_response.g.dart # 生成的序列化代码
├── network/                # 网络层
│   ├── dio_client.dart     # Dio 网络客户端
│   ├── base_service.dart   # 网络服务基类
│   ├── websocket_client.dart # WebSocket 客户端
│   └── interceptors/       # 拦截器
│       ├── auth_interceptor.dart    # 认证拦截器
│       ├── error_interceptor.dart   # 错误处理拦截器
│       └── logging_interceptor.dart # 日志拦截器
├── services/               # 业务服务层
│   └── auth_service.dart   # 认证服务示例
└── main.dart              # 应用入口
```

## 核心功能

### 1. 网络层架构

#### DioClient
- 统一的 HTTP 客户端配置
- 自动错误处理和重试机制
- 请求/响应拦截器
- 文件上传支持

#### 拦截器系统
- **认证拦截器**: 自动添加认证头和设备信息
- **错误拦截器**: 统一错误处理和友好错误提示
- **日志拦截器**: 开发环境下的详细请求日志

#### API 响应模型
- 统一的响应格式封装
- 泛型支持，类型安全
- 分页响应支持

### 2. WebSocket 实时通信

#### WebSocketClient
- 自动连接管理和重连机制
- 心跳保活机制
- 消息队列和状态管理
- 连接状态监听

#### 特性
- 自动重连（最多5次）
- 30秒心跳间隔
- 连接状态实时监听
- 消息类型化处理

### 3. 本地通知

#### 功能
- Android/iOS 本地通知支持
- 通知权限自动请求
- 通知点击事件处理
- 自定义通知样式

## 配置说明

### 应用配置 (app_config.dart)

```dart
// 后端服务地址
static const String baseUrl = 'http://192.168.5.40:8080';

// WebSocket 地址
static const String websocketUrl = 'ws://192.168.5.40:8070';

// 超时配置
static const Duration connectTimeout = Duration(seconds: 30);
static const Duration receiveTimeout = Duration(seconds: 30);
static const Duration sendTimeout = Duration(seconds: 30);
```

### 网络请求示例

```dart
// 使用 AuthService 进行登录
final authService = AuthService();
final response = await authService.login(LoginRequest(
  username: '<EMAIL>',
  password: 'password123',
));

if (response.isSuccess) {
  final loginData = response.data!;
  print('登录成功: ${loginData.userInfo.nickname}');
} else {
  print('登录失败: ${response.message}');
}
```

### WebSocket 使用示例

```dart
// 连接 WebSocket
final wsClient = WebSocketClient.instance;
await wsClient.connect(token: 'your-auth-token');

// 监听连接状态
wsClient.statusStream.listen((status) {
  print('WebSocket 状态: $status');
});

// 监听消息
wsClient.messageStream.listen((message) {
  print('收到消息: ${message.type}');
});

// 发送消息
wsClient.sendMessage(WebSocketMessage(
  type: 'chat_message',
  data: {'content': 'Hello, World!'},
));
```

## 开发指南

### 1. 添加新的 API 服务

1. 在 `services/` 目录下创建新的服务类
2. 继承 `BaseService` 类
3. 实现具体的 API 调用方法

```dart
class ChatService extends BaseService {
  Future<ApiResponse<List<Message>>> getMessages(String chatId) async {
    return await get<List<Message>>(
      '/chat/$chatId/messages',
      fromJson: (json) => (json as List)
          .map((item) => Message.fromJson(item))
          .toList(),
    );
  }
}
```

### 2. 添加新的数据模型

1. 在 `models/` 目录下创建模型类
2. 使用 `json_annotation` 注解
3. 运行代码生成命令

```bash
flutter packages pub run build_runner build
```

### 3. 自定义拦截器

1. 在 `network/interceptors/` 目录下创建拦截器
2. 继承 `Interceptor` 类
3. 在 `DioClient` 中注册拦截器

## 运行项目

### 环境要求
- Flutter SDK >= 3.7.2
- Dart SDK >= 3.0.0

### 安装依赖
```bash
flutter pub get
```

### 代码生成
```bash
flutter packages pub run build_runner build
```

### 运行应用
```bash
flutter run
```

### 代码分析
```bash
flutter analyze
```

## 注意事项

1. **网络配置**: 请根据实际后端服务地址修改 `app_config.dart` 中的配置
2. **权限配置**: Android 需要在 `android/app/src/main/AndroidManifest.xml` 中添加网络权限
3. **证书配置**: 生产环境建议使用 HTTPS 和 WSS 协议
4. **错误处理**: 所有网络请求都应该进行适当的错误处理
5. **内存管理**: 及时释放 WebSocket 连接和流订阅

## 后续开发计划

- [ ] 用户认证和授权
- [ ] 聊天界面和消息处理
- [ ] 好友管理功能
- [ ] 群组聊天功能
- [ ] 文件传输功能
- [ ] 音视频通话功能
- [ ] 推送通知集成
- [ ] 数据持久化
- [ ] 主题和国际化支持
