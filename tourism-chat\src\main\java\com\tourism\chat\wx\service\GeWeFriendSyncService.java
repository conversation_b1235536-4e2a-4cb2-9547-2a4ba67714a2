package com.tourism.chat.wx.service;


import com.tourism.chat.wx.entity.GeWeFriend;

import java.util.List;

/**
 * GeWe好友同步服务接口
 */
public interface GeWeFriendSyncService {

    /**
     * 同步所有好友信息到数据库
     * @return 同步的好友数量
     */
    int syncAllFriends();

    /**
     * 同步指定微信ID的好友信息
     *
     * @param wxIds     微信ID列表
     * @param ownerWxId
     * @return 同步的好友数量
     */
    int syncFriendsByWxIds(List<String> wxIds, String ownerWxId);

    /**
     * 获取所有好友列表
     * @return 好友列表
     */
    List<GeWeFriend> getAllFriends();

    /**
     * 获取所有群聊列表
     * @return 群聊列表
     */
    List<GeWeFriend> getAllGroups();

    /**
     * 根据关键词搜索好友
     * @param keyword 搜索关键词
     * @return 匹配的好友列表
     */
    List<GeWeFriend> searchFriends(String keyword);

    /**
     * 根据微信ID获取好友信息
     * @param wxId 微信ID
     * @return 好友信息
     */
    GeWeFriend getFriendByWxId(String wxId);

    /**
     * 强制刷新好友信息（从GeWe API重新获取）
     * @param wxId 微信ID
     * @return 更新后的好友信息
     */
    GeWeFriend refreshFriend(String wxId);

    List<GeWeFriend> getAllFriendsByOwnerWxId(String ownerWxId);
}