package com.tourism.chat.ws;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * 管理 sessionKey -> WebSocketSession 的映射。
 * sessionKey 规则：
 * - 单聊：wxId
 * - 群聊会话：wxId|chatroomId
 */
@Component
public class WebSocketSessionRegistry {

    private final ConcurrentMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    public void add(String sessionKey, WebSocketSession session) {
        sessions.put(sessionKey, session);
    }

    public void remove(String sessionKey) {
        sessions.remove(sessionKey);
    }

    public WebSocketSession get(String sessionKey) {
        return sessions.get(sessionKey);
    }

    public boolean contains(String sessionKey) {
        return sessions.containsKey(sessionKey);
    }

    /**
     * 向指定 chatroomId 的所有会话广播消息。
     * 匹配规则：sessionKey 以 |chatroomId 结尾。
     * 
     * @return 实际发送的连接数
     */
    public int broadcastToChatroom(String chatroomId, String payload) {
        if (chatroomId == null || chatroomId.isBlank())
            return 0;
        int sent = 0;
        String suffix = "|" + chatroomId;
        for (var entry : sessions.entrySet()) {
            String key = entry.getKey();
            WebSocketSession session = entry.getValue();
            if (key != null && key.endsWith(suffix) && session != null && session.isOpen()) {
                try {
                    session.sendMessage(new org.springframework.web.socket.TextMessage(payload));
                    sent++;
                } catch (Exception ignored) {
                }
            }
        }
        return sent;
    }
}
