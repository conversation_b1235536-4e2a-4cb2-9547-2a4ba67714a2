# SocketIO JWT 认证使用说明

## 概述

SocketIO服务现在已集成JWT认证，与REST API保持一致的认证机制。客户端连接时必须提供有效的JWT token，否则连接将被拒绝。

## 认证流程

### 1. 获取JWT Token
首先通过REST API登录获取JWT token：

```bash
curl -X POST http://localhost:18080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "tenantId": 1,
    "password": "password123"
  }'
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VyMTIzIiwicGhvbmUiOiIxMzgwMDEzODAwMCIsImlkIjoxLCJ0ZW5hbnRJZCI6MSwiaWF0IjoxNzM0OTQ4MDAwLCJleHAiOjE3MzUwMzQ0MDB9.signature"
  }
}
```

### 2. 建立SocketIO连接

#### 方式一：通过Authorization请求头（推荐）

```javascript
// JavaScript客户端示例
const socket = io('ws://localhost:19092', {
  extraHeaders: {
    'Authorization': 'Bearer ' + jwtToken
  },
  transports: ['websocket', 'polling']
});
```

#### 方式二：通过URL参数（备用方案）

```javascript
// JavaScript客户端示例
const socket = io('ws://localhost:19092', {
  query: {
    'token': jwtToken
  },
  transports: ['websocket', 'polling']
});
```

## 客户端连接示例

### JavaScript/Node.js

```javascript
const io = require('socket.io-client');

// JWT token (从登录接口获取)
const jwtToken = 'eyJhbGciOiJIUzI1NiJ9...';

// 建立连接
const socket = io('ws://localhost:19092', {
  extraHeaders: {
    'Authorization': 'Bearer ' + jwtToken
  },
  transports: ['websocket', 'polling']
});

// 连接成功
socket.on('connect', () => {
  console.log('✅ 连接成功:', socket.id);
});

// 连接失败
socket.on('connect_error', (error) => {
  console.error('❌ 连接失败:', error.message);
});

// 监听群聊消息
socket.on('group_message', (data) => {
  console.log('📩 收到群聊消息:', data);
});

// 监听私聊消息
socket.on('private_message', (data) => {
  console.log('📩 收到私聊消息:', data);
});

// 发送群聊消息
socket.emit('group_message', {
  tenantId: 1,
  groupId: 'group123',
  fromUserId: 'user456',
  content: 'Hello, group!'
});

// 发送私聊消息
socket.emit('private_message', {
  tenantId: 1,
  fromUserId: 'user123',
  toUserId: 'user456',
  content: 'Hello, private!'
});
```

### Python客户端

```python
import socketio
import requests

# 1. 获取JWT token
login_response = requests.post('http://localhost:18080/api/auth/login', json={
    'phone': '13800138000',
    'tenantId': 1,
    'password': 'password123'
})
jwt_token = login_response.json()['data']['token']

# 2. 建立SocketIO连接
sio = socketio.Client()

@sio.event
def connect():
    print('✅ 连接成功')

@sio.event
def connect_error(data):
    print('❌ 连接失败:', data)

@sio.event
def group_message(data):
    print('📩 收到群聊消息:', data)

@sio.event
def private_message(data):
    print('📩 收到私聊消息:', data)

# 连接时传递JWT token
sio.connect('ws://localhost:19092', headers={
    'Authorization': f'Bearer {jwt_token}'
})

# 发送消息
sio.emit('group_message', {
    'tenantId': 1,
    'groupId': 'group123',
    'fromUserId': 'user456',
    'content': 'Hello from Python!'
})

sio.wait()
```

### Java客户端

```java
import io.socket.client.IO;
import io.socket.client.Socket;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

public class SocketIOClient {
    public static void main(String[] args) {
        try {
            // JWT token (从登录接口获取)
            String jwtToken = "eyJhbGciOiJIUzI1NiJ9...";
            
            // 配置连接选项
            IO.Options options = new IO.Options();
            options.transports = new String[]{"websocket", "polling"};
            
            // 设置认证头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + jwtToken);
            options.extraHeaders = headers;
            
            // 建立连接
            Socket socket = IO.socket(URI.create("ws://localhost:19092"), options);
            
            // 连接事件
            socket.on(Socket.EVENT_CONNECT, args1 -> {
                System.out.println("✅ 连接成功");
            });
            
            socket.on(Socket.EVENT_CONNECT_ERROR, args1 -> {
                System.out.println("❌ 连接失败: " + args1[0]);
            });
            
            // 消息事件
            socket.on("group_message", args1 -> {
                System.out.println("📩 收到群聊消息: " + args1[0]);
            });
            
            socket.on("private_message", args1 -> {
                System.out.println("📩 收到私聊消息: " + args1[0]);
            });
            
            // 连接
            socket.connect();
            
            // 发送消息示例
            Map<String, Object> message = new HashMap<>();
            message.put("tenantId", 1);
            message.put("groupId", "group123");
            message.put("fromUserId", "user456");
            message.put("content", "Hello from Java!");
            socket.emit("group_message", message);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

## 认证失败的常见原因

### 1. Token格式错误
```
❌ 错误: Authorization: eyJhbGciOiJIUzI1NiJ9...
✅ 正确: Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

### 2. Token过期
```
检查JWT token的exp字段，确保未过期
```

### 3. Token签名无效
```
确保使用正确的JWT secret，与服务端配置一致
```

### 4. 缺少必要字段
```
JWT token必须包含: id, tenantId, sub(username)
```

## 服务端配置

### JWT配置 (application.yml)
```yaml
jwt:
  secret: demoSecretKey123456
  expiration: 10080  # 7天，单位：分钟
```

### SocketIO配置
```yaml
# SocketIO服务端口
socketio:
  port: 19092
  hostname: 0.0.0.0
```

## 安全注意事项

1. **HTTPS/WSS**: 生产环境建议使用HTTPS和WSS协议
2. **Token刷新**: 实现token自动刷新机制
3. **连接限制**: 考虑实现连接频率限制
4. **日志监控**: 监控认证失败的连接尝试
5. **Token撤销**: 考虑实现token黑名单机制

## 错误处理

### 客户端错误处理
```javascript
socket.on('connect_error', (error) => {
  if (error.message.includes('Authentication')) {
    // JWT认证失败，需要重新登录
    console.log('认证失败，请重新登录');
    // 跳转到登录页面或刷新token
  }
});
```

### 服务端日志
```
2024-01-01 10:00:00 WARN  - SocketIO连接被拒绝: 未提供JWT token
2024-01-01 10:00:01 WARN  - SocketIO连接被拒绝: JWT token无效
2024-01-01 10:00:02 INFO  - ✅ SocketIO连接认证成功: userId=123, tenantId=1, username=user123
```

## 测试工具

### 使用Postman测试
1. 先调用登录接口获取token
2. 使用WebSocket连接功能
3. 在Headers中添加Authorization

### 使用curl测试WebSocket
```bash
# 注意：curl不直接支持WebSocket，建议使用专门的WebSocket测试工具
wscat -c ws://localhost:19092 -H "Authorization: Bearer your_jwt_token"
```

这样就完成了SocketIO的JWT认证集成，确保了与REST API一致的安全认证机制。
