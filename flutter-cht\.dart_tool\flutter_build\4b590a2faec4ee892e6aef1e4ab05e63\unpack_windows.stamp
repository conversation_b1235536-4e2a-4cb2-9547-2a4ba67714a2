{"inputs": ["E:\\software\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "E:\\software\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_windows.dll", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_export.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_messenger.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\flutter_windows.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\icudtl.dat", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "E:\\me\\flutter-cht\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}