import 'dart:async';
import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_group_service.dart';
import '../services/gewe_service_manager.dart';
import '../widgets/group_list_item.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_widget.dart';

/// 群聊列表页面
class GroupsPage extends StatefulWidget {
  const GroupsPage({super.key});

  @override
  State<GroupsPage> createState() => _GroupsPageState();
}

class _GroupsPageState extends State<GroupsPage>
    with AutomaticKeepAliveClientMixin {
  final GeWeGroupService _groupService = GeWeGroupService();
  final ScrollController _scrollController = ScrollController();

  List<GroupListItem> _groups = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  bool get wantKeepAlive => true;

  StreamSubscription<String?>? _accountSub;

  @override
  void initState() {
    super.initState();
    _loadGroups();
    _accountSub = GeWeServiceManager.instance.accountStream.listen((_) {
      if (mounted) _loadGroups();
    });
  }

  @override
  void dispose() {
    _accountSub?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载群聊列表（根据左侧选中的账号）
  Future<void> _loadGroups() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final me = GeWeServiceManager.instance.currentUserWxId;
      if (me == null || me.isEmpty) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = '请先在左侧选择一个账号';
        });
        return;
      }

      final response = await _groupService.listGroups(wxId: me);

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          setState(() {
            _groups = response.data!;
            _isLoading = false;
          });
        } else {
          setState(() {
            _hasError = true;
            _errorMessage = response.message;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '加载群聊列表失败: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新群聊列表
  Future<void> _refreshGroups() async {
    await _loadGroups();
  }

  /// 搜索群聊
  void _searchGroups() {
    Navigator.pushNamed(context, '/search', arguments: 'groups');
  }

  /// 打开群聊详情
  void _openGroupDetail(GroupListItem group) {
    final mapped = GeWeFriend(
      wxId: group.chatroomId,
      nickName: group.nickName,
      remark: group.remark,
      smallHeadImgUrl: group.smallHeadImgUrl,
      isGroup: true,
    );
    Navigator.pushNamed(context, '/group-detail', arguments: mapped);
  }

  /// 开始群聊
  void _startGroupChat(GroupListItem group) {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': group.chatroomId,
        'contactName': group.nickName ?? group.chatroomId,
        'isGroup': true,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: RefreshIndicator(onRefresh: _refreshGroups, child: _buildBody()),
      floatingActionButton: FloatingActionButton(
        onPressed: _searchGroups,
        tooltip: '搜索群聊',
        child: const Icon(Icons.search),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _groups.isEmpty) {
      return const LoadingWidget(message: '正在加载群聊列表...');
    }

    if (_hasError && _groups.isEmpty) {
      return EmptyWidget(
        icon: Icons.error_outline,
        title: '加载失败',
        subtitle: _errorMessage,
        actionText: '重试',
        onAction: _loadGroups,
      );
    }

    if (_groups.isEmpty) {
      return const EmptyWidget(
        icon: Icons.group_outlined,
        title: '暂无群聊',
        subtitle: '点击右下角搜索按钮查找群聊',
      );
    }

    return _buildGroupsList();
  }

  Widget _buildGroupsList() {
    return ListView.separated(
      controller: _scrollController,
      itemCount: _groups.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final group = _groups[index];
        return GroupTile(
          group: group,
          onTap: () => _openGroupDetail(group),
          onChatTap: () => _startGroupChat(group),
        );
      },
    );
  }
}
