package com.tourism.chat.ws;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

/**
 * 基于 wxId + chatroomId 的 WebSocket 推送服务。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketPushService {

    private final WebSocketSessionRegistry registry;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 向单聊会话发送（sessionKey=wxId）
     */
    public boolean sendToWxId(String toWxId, String jsonPayload) {
        if (!StringUtils.hasText(toWxId)) {
            log.warn("sendToWxId: toWxId 为空");
            return false;
        }
        WebSocketSession session = registry.get(toWxId);
        if (session == null || !session.isOpen()) {
            log.info("toWxId 不在线，wxId={}", toWxId);
            return false;
        }
        try {
            session.sendMessage(new TextMessage(jsonPayload));
            return true;
        } catch (Exception e) {
            log.error("WebSocket 发送失败, wxId={}", toWxId, e);
            return false;
        }
    }

    /**
     * 向群聊会话广播（所有 sessionKey 以 |chatroomId 结尾）
     */
    public int broadcastToChatroom(String chatroomId, String jsonPayload) {
        return registry.broadcastToChatroom(chatroomId, jsonPayload);
    }

    /**
     * 便捷方法：根据回调信息构建标准推送 JSON 并发送
     */
    public void pushCallback(String fromWxId, String toWxId, Integer msgType, String content,
            Long sendTimeSec, Long receiveTimeSec) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("fromWxId", fromWxId);
        payload.put("toWxId", toWxId);
        payload.put("messageType", mapMsgType(msgType));
        payload.put("content", content);
        payload.put("atWxIds", java.util.Collections.emptyList());
        payload.put("sendTime", sendTimeSec != null ? sendTimeSec : Instant.now().getEpochSecond());
        payload.put("receiveTime", receiveTimeSec != null ? receiveTimeSec : Instant.now().getEpochSecond());
        try {
            String json = objectMapper.writeValueAsString(payload);
            boolean ok;
            if (toWxId != null && (toWxId.endsWith("@chatroom") || toWxId.startsWith("@@"))) {
                int c = broadcastToChatroom(toWxId, json);
                ok = c > 0;
            } else {
                ok = sendToWxId(toWxId, json);
            }
            if (!ok) {
                log.info("目标不在线，未发送: to={}", toWxId);
            }
        } catch (Exception e) {
            log.error("构建/发送回调消息失败", e);
        }
    }

    private String mapMsgType(Integer msgType) {
        if (msgType == null)
            return "unknown";
        return switch (msgType) {
            case 1 -> "text";
            case 3 -> "image";
            case 34 -> "voice";
            case 43 -> "video";
            case 49 -> "file"; // 或 link/app 等，后续细分
            case 48 -> "location";
            case 47 -> "emoji";
            case 10000 -> "system";
            default -> "unknown";
        };
    }
}
