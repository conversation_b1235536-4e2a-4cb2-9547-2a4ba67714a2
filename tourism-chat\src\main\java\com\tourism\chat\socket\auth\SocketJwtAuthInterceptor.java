package com.tourism.chat.socket.auth;

import com.corundumstudio.socketio.AuthorizationListener;
import com.corundumstudio.socketio.AuthorizationResult;
import com.corundumstudio.socketio.HandshakeData;
import com.tourism.chat.auth.JwtTokenProvider;
import com.tourism.chat.tenant.TenantContextHolder;
import com.tourism.chat.tenant.UserContext;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * SocketIO JWT 认证拦截器
 * 验证客户端连接时提供的JWT token
 */
@Component
@Slf4j
public class SocketJwtAuthInterceptor implements AuthorizationListener {

    private final JwtTokenProvider jwtTokenProvider;

    public SocketJwtAuthInterceptor(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Override
    public AuthorizationResult getAuthorizationResult(HandshakeData handshakeData) {
        if (isAuthorized(handshakeData)) {
            return AuthorizationResult.SUCCESSFUL_AUTHORIZATION;
        }
        return AuthorizationResult.FAILED_AUTHORIZATION;
    }

    public boolean isAuthorized(HandshakeData handshakeData) {
        try {
            // 1. 从请求头中获取 Authorization token
            String authHeader = handshakeData.getHttpHeaders().get("Authorization");
            String token = extractToken(authHeader);

            if (token == null) {
                // 2. 尝试从URL参数中获取token (备用方案)
                token = handshakeData.getSingleUrlParam("token");
            }

            if (token == null) {
                log.warn("SocketIO连接被拒绝: 未提供JWT token");
                return false;
            }

            // 3. 验证JWT token
            if (!jwtTokenProvider.validateToken(token)) {
                log.warn("SocketIO连接被拒绝: JWT token无效");
                return false;
            }

            // 4. 解析token获取用户信息
            Claims claims = jwtTokenProvider.parseClaims(token);
            Long userId = claims.get("id", Long.class);
            Long tenantId = claims.get("tenantId", Long.class);
            String phone = claims.get("phone", String.class);
            String username = claims.getSubject();

            // 5. 验证必要的用户信息
            if (userId == null || tenantId == null) {
                log.warn("SocketIO连接被拒绝: JWT token中缺少必要的用户信息 userId={}, tenantId={}", userId, tenantId);
                return false;
            }

            // 6. 将用户信息存储到握手数据中，供后续使用
            handshakeData.getUrlParams().put("userId", Collections.singletonList(userId.toString()));
            handshakeData.getUrlParams().put("tenantId", Collections.singletonList(tenantId.toString()));
            handshakeData.getUrlParams().put("username", Collections.singletonList(username));
            if (phone != null) {
                handshakeData.getUrlParams().put("phone", Collections.singletonList(phone));
            }

            // 7. 设置用户上下文 (可选，用于后续业务逻辑)
            UserContext userContext = new UserContext(userId, tenantId, phone);
            TenantContextHolder.setUserContext(userContext);

            log.info("✅ SocketIO连接认证成功: userId={}, tenantId={}, username={}", userId, tenantId, username);
            return true;

        } catch (Exception e) {
            log.error("SocketIO连接认证失败", e);
            return false;
        }
    }

    /**
     * 从Authorization头中提取JWT token
     * 支持格式: "Bearer <token>" 或直接 "<token>"
     */
    private String extractToken(String authHeader) {
        if (authHeader == null || authHeader.trim().isEmpty()) {
            return null;
        }

        authHeader = authHeader.trim();

        // 支持 "Bearer token" 格式
        if (authHeader.toLowerCase().startsWith("bearer ")) {
            return authHeader.substring(7);
        }

        // 直接返回token (兼容直接传token的情况)
        return authHeader;
    }

    /**
     * 从握手数据中获取用户ID
     */
    public static String getUserId(HandshakeData handshakeData) {
        String userIdStr = handshakeData.getSingleUrlParam("userId");
        return userIdStr;
    }

    /**
     * 从握手数据中获取租户ID
     */
    public static String getTenantId(HandshakeData handshakeData) {
        String tenantIdStr = handshakeData.getSingleUrlParam("tenantId");
        return tenantIdStr;
    }

    /**
     * 从握手数据中获取用户名
     */
    public static String getUsername(HandshakeData handshakeData) {
        return handshakeData.getSingleUrlParam("username");
    }

    /**
     * 从握手数据中获取手机号
     */
    public static String getPhone(HandshakeData handshakeData) {
        return handshakeData.getSingleUrlParam("phone");
    }

}
