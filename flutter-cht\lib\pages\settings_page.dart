import 'package:flutter/material.dart';
import 'package:im_app/network/websocket_client.dart';
import '../storage/token_storage.dart';
import '../network/dio_client.dart';
import '../services/gewe_service_manager.dart';

/// 设置页：账号、连接与调试
class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _wsConnected = false;
  String? _currentWxId;

  @override
  void initState() {
    super.initState();
    _currentWxId = GeWeServiceManager.instance.currentUserWxId;
    _wsConnected = GeWeServiceManager.instance.isConnected;
    GeWeServiceManager.instance.websocketService.connectionStatusStream.listen((s) {
      if (!mounted) return;
      setState(() => _wsConnected = s == WebSocketStatus.connected);
    });
  }

  Future<void> _logout() async {
    await GeWeServiceManager.instance.disconnect();
    await TokenStorage.clearToken();
    DioClient.instance.setAuthToken("");
    if (!mounted) return;
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (_) => false);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('设置')),
      body: ListView(
        children: [
          const SizedBox(height: 8),
          ListTile(
            leading: const Icon(Icons.person_outline),
            title: const Text('当前账号'),
            subtitle: Text(_currentWxId ?? '未选择'),
            trailing:
                _wsConnected
                    ? const Chip(
                      label: Text('WS已连接'),
                      backgroundColor: Colors.greenAccent,
                    )
                    : const Chip(
                      label: Text('未连接'),
                      backgroundColor: Colors.grey,
                    ),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.wifi_tethering),
            title: const Text('WebSocket 连接'),
            subtitle: const Text('切换连接状态'),
            value: _wsConnected,
            onChanged: (v) async {
              if (v) {
                await GeWeServiceManager.instance.connect();
              } else {
                await GeWeServiceManager.instance.disconnect();
              }
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.delete_outline),
            title: const Text('清除登录状态并退出'),
            onTap: _logout,
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              '调试信息\n- 当前WxId: ${_currentWxId ?? '-'}\n- WS连接: ${_wsConnected ? '已连接' : '未连接'}',
              style: theme.textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
