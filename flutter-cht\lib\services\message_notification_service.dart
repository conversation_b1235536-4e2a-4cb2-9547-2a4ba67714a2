import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/gewe_message.dart';
import '../models/gewe_friend.dart';
import 'gewe_friend_service.dart';

/// 消息通知服务
class MessageNotificationService {
  static MessageNotificationService? _instance;

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  final GeWeFriendService _friendService = GeWeFriendService();

  // 好友信息缓存
  final Map<String, GeWeFriend> _friendCache = {};

  // 全局导航器键
  static GlobalKey<NavigatorState>? navigatorKey;

  // 通知ID计数器
  int _notificationId = 1000;

  MessageNotificationService._internal();

  static MessageNotificationService get instance {
    _instance ??= MessageNotificationService._internal();
    return _instance!;
  }

  /// 初始化通知服务
  Future<void> initialize() async {
    try {
      // Android 初始化设置
      const androidInitializationSettings = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );

      // iOS 初始化设置
      const iosInitializationSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // 初始化设置
      const initializationSettings = InitializationSettings(
        android: androidInitializationSettings,
        iOS: iosInitializationSettings,
      );

      // 初始化插件
      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 请求 Android 13+ 通知权限
      await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();

      if (kDebugMode) {
        print('✅ Message notification service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize notification service: $e');
      }
    }
  }

  /// 显示新消息通知
  Future<void> showMessageNotification(GeWeMessage message) async {
    try {
      // 获取发送者信息
      final senderInfo = await _getSenderInfo(message.fromWxId);
      final senderName = senderInfo?.name ?? message.fromWxId ?? '未知用户';

      // 构建通知内容
      final title =
          message.isGroupMessage
              ? '群聊消息 - ${await _getGroupName(message.toWxId)}'
              : senderName;

      final body = _formatMessageContent(message, senderName);

      // 构建通知详情
      const androidDetails = AndroidNotificationDetails(
        'gewe_messages',
        'GeWe消息',
        channelDescription: 'GeWe即时消息通知',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // 显示通知
      await _notificationsPlugin.show(
        _getNextNotificationId(),
        title,
        body,
        notificationDetails,
        payload: _createNotificationPayload(message),
      );

      if (kDebugMode) {
        print('📱 Notification shown: $title - $body');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to show notification: $e');
      }
    }
  }

  /// 获取发送者信息
  Future<GeWeFriend?> _getSenderInfo(String? wxId) async {
    if (wxId == null) return null;

    // 先从缓存获取
    if (_friendCache.containsKey(wxId)) {
      return _friendCache[wxId];
    }

    try {
      // 从API获取
      final response = await _friendService.getFriendDetail(wxId);
      if (response.isSuccess && response.data != null) {
        _friendCache[wxId] = response.data!;
        return response.data!;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get sender info for $wxId: $e');
      }
    }

    return null;
  }

  /// 获取群聊名称
  Future<String> _getGroupName(String? groupWxId) async {
    if (groupWxId == null) return '未知群聊';

    final groupInfo = await _getSenderInfo(groupWxId);
    return groupInfo?.name ?? '未知群聊';
  }

  /// 格式化消息内容
  String _formatMessageContent(GeWeMessage message, String senderName) {
    final content = message.content ?? '';

    switch (message.messageType) {
      case MessageType.text:
        return message.isGroupMessage ? '$senderName: $content' : content;
      case MessageType.image:
        return message.isGroupMessage ? '$senderName: [图片]' : '[图片]';
      case MessageType.voice:
        return message.isGroupMessage ? '$senderName: [语音]' : '[语音]';
      case MessageType.video:
        return message.isGroupMessage ? '$senderName: [视频]' : '[视频]';
      case MessageType.file:
        return message.isGroupMessage ? '$senderName: [文件]' : '[文件]';
      case MessageType.location:
        return message.isGroupMessage ? '$senderName: [位置]' : '[位置]';
      case MessageType.emoji:
        return message.isGroupMessage ? '$senderName: [表情]' : '[表情]';
      case MessageType.system:
        return content;
      default:
        return message.isGroupMessage ? '$senderName: [消息]' : '[消息]';
    }
  }

  /// 创建通知载荷
  String _createNotificationPayload(GeWeMessage message) {
    return 'gewe_message:${message.fromWxId}:${message.toWxId}:${message.msgId}';
  }

  /// 获取下一个通知ID
  int _getNextNotificationId() {
    return ++_notificationId;
  }

  /// 处理通知点击事件
  void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('📱 Notification tapped: ${response.payload}');
    }

    if (response.payload != null) {
      _handleNotificationPayload(response.payload!);
    }
  }

  /// 处理通知载荷
  void _handleNotificationPayload(String payload) {
    try {
      if (payload.startsWith('gewe_message:')) {
        final parts = payload.split(':');
        if (parts.length >= 4) {
          final fromWxId = parts[1];
          final toWxId = parts[2];
          final msgId = parts[3];

          // TODO: 导航到对应的聊天页面
          _navigateToChat(fromWxId, toWxId, msgId);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to handle notification payload: $e');
      }
    }
  }

  /// 导航到聊天页面
  void _navigateToChat(String fromWxId, String toWxId, String msgId) async {
    if (navigatorKey?.currentContext == null) {
      if (kDebugMode) {
        print('❌ Navigator context not available');
      }
      return;
    }

    try {
      // 获取发送者信息
      final friend = await _getFriendInfo(fromWxId);
      if (friend != null) {
        // 导航到聊天页面
        navigatorKey!.currentState?.pushNamed(
          '/chat',
          arguments: {
            'contactWxId': fromWxId,
            'contactName': friend.name,
            'isGroup': friend.isGroup,
          },
        );

        if (kDebugMode) {
          print('🔗 Navigated to chat with ${friend.name}');
        }
      } else {
        if (kDebugMode) {
          print('❌ Friend info not found for $fromWxId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Navigation error: $e');
      }
    }
  }

  /// 获取好友信息（带缓存）
  Future<GeWeFriend?> _getFriendInfo(String wxId) async {
    // 先从缓存中查找
    if (_friendCache.containsKey(wxId)) {
      return _friendCache[wxId];
    }

    try {
      // 从服务器获取
      final response = await _friendService.getFriendDetail(wxId);
      if (response.isSuccess && response.data != null) {
        final friend = response.data!;
        _friendCache[wxId] = friend; // 缓存结果
        return friend;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get friend info for $wxId: $e');
      }
    }

    return null;
  }

  /// 设置全局导航器键
  static void setNavigatorKey(GlobalKey<NavigatorState> key) {
    navigatorKey = key;
  }

  /// 清除所有通知
  Future<void> clearAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  /// 清除指定通知
  Future<void> clearNotification(int id) async {
    await _notificationsPlugin.cancel(id);
  }

  /// 清理缓存
  void clearCache() {
    _friendCache.clear();
  }
}
