package com.tourism.chat.wx.vo;

import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.entity.GeWeMessage;
import lombok.Getter;
import lombok.Setter;

/**
 * GeWe好友信息VO
 */
@Getter
@Setter
public class GeWeFriendVO {

    private Long id;
    private String wxId;
    private String nickName;
    private String pyInitial;
    private String quanPin;
    private Integer sex;
    private String remark;
    private String remarkPyInitial;
    private String remarkQuanPin;
    private String signature;
    private String alias;
    private String snsBgImg;
    private String country;
    private String province;
    private String city;
    private String bigHeadImgUrl;
    private String smallHeadImgUrl;
    private Boolean isGroup;
    private String displayName; // 显示名称（备注优先，否则昵称）
    private Integer unreadCount;
    private GeWeMessage lastMessage;

    /**
     * 从实体转换为VO
     */
    public static GeWeFriendVO from(GeWeFriend friend) {
        if (friend == null) {
            return null;
        }

        GeWeFriendVO vo = new GeWeFriendVO();
        vo.setId(friend.getId());
        vo.setWxId(friend.getWxId());
        vo.setNickName(friend.getNickName());
        vo.setPyInitial(friend.getPyInitial());
        vo.setQuanPin(friend.getQuanPin());
        vo.setSex(friend.getSex());
        vo.setRemark(friend.getRemark());
        vo.setRemarkPyInitial(friend.getRemarkPyInitial());
        vo.setRemarkQuanPin(friend.getRemarkQuanPin());
        vo.setSignature(friend.getSignature());
        vo.setAlias(friend.getAlias());
        vo.setSnsBgImg(friend.getSnsBgImg());
        vo.setCountry(friend.getCountry());
        vo.setProvince(friend.getProvince());
        vo.setCity(friend.getCity());
        vo.setBigHeadImgUrl(friend.getBigHeadImgUrl());
        vo.setSmallHeadImgUrl(friend.getSmallHeadImgUrl());
        vo.setIsGroup(friend.getIsGroup());

        // 设置显示名称
        String displayName = friend.getRemark();
        if (displayName == null || displayName.trim().isEmpty()) {
            displayName = friend.getNickName();
        }
        vo.setDisplayName(displayName);

        return vo;
    }
}