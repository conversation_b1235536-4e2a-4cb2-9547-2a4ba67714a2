import '../models/api_response.dart';
import '../network/base_service.dart';

/// 用户认证相关的数据模型
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  Map<String, dynamic> toJson() => {
    'username': username,
    'password': password,
  };
}

class LoginResponse {
  final String token;
  final String refreshToken;
  final UserInfo userInfo;

  LoginResponse({
    required this.token,
    required this.refreshToken,
    required this.userInfo,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
    token: json['token'] as String,
    refreshToken: json['refreshToken'] as String,
    userInfo: UserInfo.fromJson(json['userInfo'] as Map<String, dynamic>),
  );
}

class UserInfo {
  final String id;
  final String username;
  final String nickname;
  final String? avatar;
  final String? email;
  final String? phone;

  UserInfo({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    this.email,
    this.phone,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => UserInfo(
    id: json['id'] as String,
    username: json['username'] as String,
    nickname: json['nickname'] as String,
    avatar: json['avatar'] as String?,
    email: json['email'] as String?,
    phone: json['phone'] as String?,
  );
}

/// 认证服务
class AuthService extends BaseService {
  /// 用户登录
  Future<ApiResponse<LoginResponse>> login(LoginRequest request) async {
    return await post<LoginResponse>(
      '/auth/login',
      data: request.toJson(),
      fromJson: (json) => LoginResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 用户注册
  Future<ApiResponse<UserInfo>> register({
    required String username,
    required String password,
    required String nickname,
    String? email,
    String? phone,
  }) async {
    return await post<UserInfo>(
      '/auth/register',
      data: {
        'username': username,
        'password': password,
        'nickname': nickname,
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
      },
      fromJson: (json) => UserInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 刷新 Token
  Future<ApiResponse<String>> refreshToken(String refreshToken) async {
    return await post<String>(
      '/auth/refresh',
      data: {'refreshToken': refreshToken},
      fromJson: (json) => json['token'] as String,
    );
  }

  /// 登出
  Future<ApiResponse<void>> logout() async {
    return await post<void>('/auth/logout');
  }

  /// 获取用户信息
  Future<ApiResponse<UserInfo>> getUserInfo() async {
    return await get<UserInfo>(
      '/user/info',
      fromJson: (json) => UserInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 更新用户信息
  Future<ApiResponse<UserInfo>> updateUserInfo({
    String? nickname,
    String? email,
    String? phone,
    String? avatar,
  }) async {
    return await put<UserInfo>(
      '/user/info',
      data: {
        if (nickname != null) 'nickname': nickname,
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
        if (avatar != null) 'avatar': avatar,
      },
      fromJson: (json) => UserInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 上传头像
  Future<ApiResponse<String>> uploadAvatar(String filePath) async {
    return await upload<String>(
      '/user/avatar',
      filePath,
      fromJson: (json) => json['url'] as String,
    );
  }
}
