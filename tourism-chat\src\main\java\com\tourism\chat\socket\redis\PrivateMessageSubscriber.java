package com.tourism.chat.socket.redis;

import com.alibaba.fastjson2.JSON;
import com.corundumstudio.socketio.SocketIOServer;
import com.tourism.chat.constants.RedisKeyConstants;
import com.tourism.chat.socket.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 私聊消息Redis订阅者
 * 专门处理私聊消息的分发
 */
@Component
@Slf4j
public class PrivateMessageSubscriber implements MessageListener {

    private final SocketIOServer server;

    public PrivateMessageSubscriber(SocketIOServer server) {
        this.server = server;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String body = new String(message.getBody());
            String channel = new String(pattern);
            
            log.debug("收到私聊消息频道: {}, 内容: {}", channel, body);
            
            // 解析消息
            ChatMessage chat = JSON.parseObject(body, ChatMessage.class);
            
            // 验证是否为私聊消息
            if (chat.getToUserId() == null) {
                log.warn("私聊消息频道收到非私聊消息: {}", chat);
                return;
            }
            
            // 构建房间名称
            String room = RedisKeyConstants.Room.userRoom(chat.getTenantId(), chat.getToUserId());

            // 转发给对应房间的所有客户端
            server.getRoomOperations(room).sendEvent(RedisKeyConstants.Event.PRIVATE_MESSAGE, chat);
            
            log.info("✅ 私聊消息已转发到房间 [{}]: 发送者={}, 接收者={}, 内容={}", 
                    room, chat.getFromUserId(), chat.getToUserId(), chat.getContent());
                    
        } catch (Exception e) {
            log.error("处理私聊消息时发生错误", e);
        }
    }
}
