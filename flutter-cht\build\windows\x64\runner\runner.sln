﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{84CA7734-4C9E-36F6-92ED-723DA9D903B5}"
	ProjectSection(ProjectDependencies) = postProject
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4} = {3CEFBDEA-772E-3398-A26F-C4479B0440D4}
		{66AF9822-0598-3C10-87C3-B6CB36534102} = {66AF9822-0598-3C10-87C3-B6CB36534102}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{DF978EF8-DD4B-390C-87A1-451C06DB8C73}"
	ProjectSection(ProjectDependencies) = postProject
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5} = {84CA7734-4C9E-36F6-92ED-723DA9D903B5}
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4} = {3CEFBDEA-772E-3398-A26F-C4479B0440D4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{3CEFBDEA-772E-3398-A26F-C4479B0440D4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\flutter\flutter_assemble.vcxproj", "{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}"
	ProjectSection(ProjectDependencies) = postProject
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4} = {3CEFBDEA-772E-3398-A26F-C4479B0440D4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "..\flutter\flutter_wrapper_app.vcxproj", "{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}"
	ProjectSection(ProjectDependencies) = postProject
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4} = {3CEFBDEA-772E-3398-A26F-C4479B0440D4}
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38} = {0E7E1762-5023-3FC1-AEF3-C1C76A105F38}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "im_app", "im_app.vcxproj", "{66AF9822-0598-3C10-87C3-B6CB36534102}"
	ProjectSection(ProjectDependencies) = postProject
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4} = {3CEFBDEA-772E-3398-A26F-C4479B0440D4}
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38} = {0E7E1762-5023-3FC1-AEF3-C1C76A105F38}
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA} = {02EF1031-7E47-37BB-A3E7-84E60DFBECCA}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Debug|x64.ActiveCfg = Debug|x64
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Debug|x64.Build.0 = Debug|x64
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Profile|x64.ActiveCfg = Profile|x64
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Profile|x64.Build.0 = Profile|x64
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Release|x64.ActiveCfg = Release|x64
		{84CA7734-4C9E-36F6-92ED-723DA9D903B5}.Release|x64.Build.0 = Release|x64
		{DF978EF8-DD4B-390C-87A1-451C06DB8C73}.Debug|x64.ActiveCfg = Debug|x64
		{DF978EF8-DD4B-390C-87A1-451C06DB8C73}.Profile|x64.ActiveCfg = Profile|x64
		{DF978EF8-DD4B-390C-87A1-451C06DB8C73}.Release|x64.ActiveCfg = Release|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Debug|x64.ActiveCfg = Debug|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Debug|x64.Build.0 = Debug|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Profile|x64.ActiveCfg = Profile|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Profile|x64.Build.0 = Profile|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Release|x64.ActiveCfg = Release|x64
		{3CEFBDEA-772E-3398-A26F-C4479B0440D4}.Release|x64.Build.0 = Release|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Debug|x64.ActiveCfg = Debug|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Debug|x64.Build.0 = Debug|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Profile|x64.ActiveCfg = Profile|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Profile|x64.Build.0 = Profile|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Release|x64.ActiveCfg = Release|x64
		{0E7E1762-5023-3FC1-AEF3-C1C76A105F38}.Release|x64.Build.0 = Release|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Debug|x64.ActiveCfg = Debug|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Debug|x64.Build.0 = Debug|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Profile|x64.ActiveCfg = Profile|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Profile|x64.Build.0 = Profile|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Release|x64.ActiveCfg = Release|x64
		{02EF1031-7E47-37BB-A3E7-84E60DFBECCA}.Release|x64.Build.0 = Release|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Debug|x64.ActiveCfg = Debug|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Debug|x64.Build.0 = Debug|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Profile|x64.ActiveCfg = Profile|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Profile|x64.Build.0 = Profile|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Release|x64.ActiveCfg = Release|x64
		{66AF9822-0598-3C10-87C3-B6CB36534102}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C7523BC1-7F27-37CF-AD8D-94DFA1F52A35}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
