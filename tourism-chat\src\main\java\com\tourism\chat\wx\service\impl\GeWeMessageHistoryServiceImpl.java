package com.tourism.chat.wx.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.group.mapper.GeweGroupMessageMapper;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.model.WeChatMessage;
import com.tourism.chat.wx.service.GeWeMessageHistoryService;
import com.tourism.chat.wx.service.GeWeMessageService;
import com.tourism.chat.wx.service.GeweGroupMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GeWe消息历史服务实现类
 */
@Service
@Slf4j
public class GeWeMessageHistoryServiceImpl implements GeWeMessageHistoryService {

    @Resource
    private GeWeMessageService geWeMessageService;


    @Resource
    private GeweGroupMessageService groupMessageService;

    @Override
    @Transactional
    public GeWeMessage saveMessage(GeWeMessage message) {
        boolean save = geWeMessageService.save(message);
        return message;
    }

    @Override
    @Transactional
    public GeWeMessage saveSentMessage(String fromWxId, String toWxId, String content,
                                       Integer messageType, Boolean isGroup, String atWxIds, WeChatMessage rtMessage) {
        GeWeMessage message = new GeWeMessage();
        message.setFromWxId(fromWxId);
        message.setToWxId(toWxId);
        message.setContent(content);
        message.setMessageType(messageType != null ? messageType : 1); // 默认文本消息
        message.setIsGroup(isGroup != null ? isGroup : false);
        message.setAtWxIds(atWxIds);
        message.setDirection(1); // 发送
        message.setStatus(2); // 已发送
        message.setMessageId(rtMessage.getNewMsgId());
        message.setSendTime(LocalDateTime.now());

        log.info("保存发送消息: {} -> {}, 内容: {}", fromWxId, toWxId, content);
        geWeMessageService.save(message);
        return message;
    }

    @Override
    @Transactional
    public GeWeMessage saveReceivedMessage(String messageId, String fromWxId, String toWxId,
                                           String content, Integer messageType, Boolean isGroup,
                                           String atWxIds, LocalDateTime sendTime) {
        return saveReceivedMessage(messageId, fromWxId, toWxId, content, messageType, isGroup, atWxIds, sendTime, null);
    }

    @Override
    @Transactional
    public GeWeMessage saveReceivedMessage(String messageId, String fromWxId, String toWxId,
                                           String content, Integer messageType, Boolean isGroup,
                                           String atWxIds, LocalDateTime sendTime, String extraData) {
        // 检查消息是否已存在
        if (messageId != null && geWeMessageService.findByMessageId(messageId).isPresent()) {
            log.warn("消息已存在，跳过保存: {}", messageId);
            return null;
        }

        GeWeMessage message = new GeWeMessage();
        message.setMessageId(messageId);
        message.setFromWxId(fromWxId);
        message.setToWxId(toWxId);
        message.setContent(content);
        message.setMessageType(messageType != null ? messageType : 1); // 默认文本消息
        message.setIsGroup(isGroup != null ? isGroup : false);
        message.setAtWxIds(atWxIds);
        message.setDirection(2); // 接收
        message.setStatus(4); // 已接收
        message.setSendTime(sendTime != null ? sendTime : LocalDateTime.now());
        message.setReceiveTime(LocalDateTime.now());
        message.setExtraData(extraData); // 设置扩展数据

        log.info("保存接收消息: {} -> {}, 内容: {}, 扩展数据: {}", fromWxId, toWxId, content, extraData != null ? "有" : "无");
        geWeMessageService.save(message);
        return message;
    }

    @Override
    public Page<GeWeMessage> getChatHistory(String wxId1, String wxId2, Pageable pageable) {
        return geWeMessageService.findChatHistory(wxId1, wxId2, pageable);
    }

    @Override
    public Page<GeweGroupMessage> getGroupChatHistory(String groupWxId, Pageable pageable) {
        return groupMessageService.findGroupChatHistory(groupWxId, pageable);
    }

    @Override
    public Page<GeWeMessage> getUserAllMessages(String wxId, Pageable pageable) {
        return geWeMessageService.findUserAllMessages(wxId, pageable);
    }

    @Override
    public Page<GeWeMessage> getMessagesByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                    Pageable pageable) {
        return geWeMessageService.findMessagesByTimeRange(startTime, endTime, pageable);
    }

    @Override
    public long countChatMessages(String wxId1, String wxId2) {
        return geWeMessageService.countChatMessages(wxId1, wxId2);
    }

    @Override
    public long countGroupMessages(String groupWxId) {
        return geWeMessageService.countGroupMessages(groupWxId);
    }

    @Override
    public List<String> getRecentChatContacts(String wxId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return geWeMessageService.findRecentChatContacts(wxId, pageable);
    }

    @Override
    public List<String> getRecentGroups(String wxId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return geWeMessageService.findRecentGroups(wxId, pageable);
    }

    @Override
    @Transactional
    public void updateMessageStatus(Long messageId, Integer status) {
        geWeMessageService.findById(messageId).ifPresent(message -> {
            message.setStatus(status);
            geWeMessageService.save(message);
            log.info("更新消息状态: {} -> {}", messageId, status);
        });
    }

    @Override
    public GeWeMessage findByMessageId(String messageId) {
        return geWeMessageService.findByMessageId(messageId).orElse(null);
    }

    @Override
    @Transactional
    public void deleteMessagesBefore(LocalDateTime beforeTime) {
        geWeMessageService.deleteMessagesBefore(beforeTime);
        log.info("删除 {} 之前的消息", beforeTime);
    }
}
