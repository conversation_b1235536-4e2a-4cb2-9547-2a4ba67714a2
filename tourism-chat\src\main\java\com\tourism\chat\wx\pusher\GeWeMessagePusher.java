// package com.tourism.chat.wx.pusher;
//
// import com.tourism.chat.wx.entity.GeWeMessage;
// import com.tourism.chat.wx.request.GeWeMessageCallbackRequest;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Component;
//
//
//
// /**
//  * GeWe消息推送器
//  * 基于现有的消息推送系统推送GeWe消息
//  */
// @Component
// @Slf4j
// public class GeWeMessagePusher extends BaseMessagePusher {
//
//     /**
//      * 推送GeWe回调消息到前端
//      */
//     public void pushCallbackMessage(GeWeMessageCallbackRequest request) {
//         try {
//             log.info("开始推送GeWe回调消息: msgId={}, from={}, to={}",
//                     request.getMsgId(), request.getFromWxId(), request.getToWxId());
//
//             EventMessage message = convertCallbackToMessage(request);
//
//             log.info("转换后的消息: action={}, content={}", message.getAction(), message.getContent());
//
//             // 获取当前用户ID，如果没有则广播给多个用户
//             Long currentUid = UidHolder.getUid();
//             java.util.List<Long> userIds;
//             currentUid = 10000L;
//             if (currentUid != null) {
//                 log.info("推送给当前用户: {}", currentUid);
//                 userIds = java.util.Arrays.asList(currentUid);
//             } else {
//                 log.info("当前用户ID为空，广播给多个用户");
//                 userIds = java.util.Arrays.asList(1L, 2L, 3L, 4L, 5L);
//             }
//
//             super.push(message, userIds);
//
//             log.info("推送GeWe回调消息成功: msgId={}, from={}, to={}",
//                     request.getMsgId(), request.getFromWxId(), request.getToWxId());
//
//         } catch (Exception e) {
//             log.error("推送GeWe回调消息失败", e);
//         }
//     }
//
//     /**
//      * 推送GeWe消息实体到前端
//      */
//     public void pushGeWeMessage(GeWeMessage geWeMessage) {
//         try {
//             EventMessage message = convertGeWeMessageToMessage(geWeMessage);
//
//             // 获取当前用户ID，如果没有则广播给多个用户
//             Long currentUid = UidHolder.getUid();
//             java.util.List<Long> userIds;
//
//             if (currentUid != null) {
//                 log.info("推送给当前用户: {}", currentUid);
//                 userIds = java.util.Arrays.asList(currentUid);
//             } else {
//                 log.info("当前用户ID为空，广播给多个用户");
//                 userIds = java.util.Arrays.asList(1L, 2L, 3L, 4L, 5L);
//             }
//
//             super.push(message, userIds);
//
//             log.info("推送GeWe消息成功: messageId={}, from={}, to={}",
//                     geWeMessage.getMessageId(), geWeMessage.getFromWxId(), geWeMessage.getToWxId());
//
//         } catch (Exception e) {
//             log.error("推送GeWe消息失败", e);
//         }
//     }
//
//     /**
//      * 将GeWe回调请求转换为事件消息
//      */
//     private EventMessage convertCallbackToMessage(GeWeMessageCallbackRequest request) {
//         EventMessage message = new EventMessage();
//
//         // 设置消息基本信息
//         message.setAction("gewe"); // 自定义action
//         message.setSender(0L); // GeWe系统发送
//         message.setReceiver(1L); // 接收用户ID，实际应该根据业务逻辑确定
//
//         // 构建消息内容
//         StringBuilder contentBuilder = new StringBuilder();
//         contentBuilder.append("{");
//         contentBuilder.append("\"type\":\"gewe\",");
//         contentBuilder.append("\"typeName\":\"").append(request.getTypeName()).append("\",");
//         contentBuilder.append("\"msgId\":").append(request.getMsgId()).append(",");
//         contentBuilder.append("\"from\":\"").append(request.getFromWxId()).append("\",");
//         contentBuilder.append("\"to\":\"").append(request.getToWxId()).append("\",");
//         contentBuilder.append("\"msgType\":").append(request.getMsgType()).append(",");
//         contentBuilder.append("\"content\":\"").append(escapeJson(request.getContent())).append("\",");
//         contentBuilder.append("\"createTime\":").append(request.getCreateTime()).append(",");
//         contentBuilder.append("\"pushContent\":\"").append(escapeJson(request.getPushContent())).append("\"");
//         contentBuilder.append("}");
//
//         message.setContent(contentBuilder.toString());
//         message.setCreateTime(System.currentTimeMillis());
//
//         return message;
//     }
//
//     /**
//      * 将GeWe消息实体转换为事件消息
//      */
//     private EventMessage convertGeWeMessageToMessage(GeWeMessage geWeMessage) {
//         EventMessage message = new EventMessage();
//
//         // 设置消息基本信息
//         message.setAction("gewe_message"); // 自定义action
//         message.setSender(0L); // GeWe系统发送
//         message.setReceiver(1L); // 接收用户ID
//
//         // 构建消息内容
//         StringBuilder contentBuilder = new StringBuilder();
//         contentBuilder.append("{");
//         contentBuilder.append("\"type\":\"gewe_message\",");
//         contentBuilder.append("\"id\":").append(geWeMessage.getId()).append(",");
//         contentBuilder.append("\"messageId\":\"").append(geWeMessage.getMessageId()).append("\",");
//         contentBuilder.append("\"from\":\"").append(geWeMessage.getFromWxId()).append("\",");
//         contentBuilder.append("\"to\":\"").append(geWeMessage.getToWxId()).append("\",");
//         contentBuilder.append("\"messageType\":").append(geWeMessage.getMessageType()).append(",");
//         contentBuilder.append("\"content\":\"").append(escapeJson(geWeMessage.getContent())).append("\",");
//         contentBuilder.append("\"isGroup\":").append(geWeMessage.getIsGroup()).append(",");
//         contentBuilder.append("\"direction\":").append(geWeMessage.getDirection()).append(",");
//         contentBuilder.append("\"status\":").append(geWeMessage.getStatus()).append(",");
//         contentBuilder.append("\"sendTime\":\"").append(geWeMessage.getSendTime()).append("\"");
//         if (geWeMessage.getExtraData() != null) {
//             contentBuilder.append(",\"extraData\":").append(geWeMessage.getExtraData());
//         }
//         contentBuilder.append("}");
//
//         message.setContent(contentBuilder.toString());
//         // 将LocalDateTime转换为时间戳
//         long timestamp = geWeMessage.getCreateTime() != null ?
//             geWeMessage.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() :
//             System.currentTimeMillis();
//         message.setCreateTime(timestamp);
//
//         return message;
//     }
//
//     /**
//      * 转义JSON字符串中的特殊字符
//      */
//     private String escapeJson(String str) {
//         if (str == null) {
//             return "";
//         }
//         return str.replace("\\", "\\\\")
//                   .replace("\"", "\\\"")
//                   .replace("\n", "\\n")
//                   .replace("\r", "\\r")
//                   .replace("\t", "\\t");
//     }
// }
