{"buildFiles": ["E:\\software\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\me\\flutter-cht\\android\\app\\.cxx\\Debug\\x494f2w2\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\me\\flutter-cht\\android\\app\\.cxx\\Debug\\x494f2w2\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}