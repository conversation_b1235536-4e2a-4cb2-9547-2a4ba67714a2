import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_friend_service.dart';

/// 好友详情页面
class FriendDetailPage extends StatefulWidget {
  final GeWeFriend friend;

  const FriendDetailPage({
    super.key,
    required this.friend,
  });

  @override
  State<FriendDetailPage> createState() => _FriendDetailPageState();
}

class _FriendDetailPageState extends State<FriendDetailPage> {
  final GeWeFriendService _friendService = GeWeFriendService();
  
  late GeWeFriend _friend;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _friend = widget.friend;
  }

  /// 刷新好友信息
  Future<void> _refreshFriendInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _friendService.refreshFriend(_friend.wxId);
      if (response.isSuccess) {
        // 重新获取好友详情
        final detailResponse = await _friendService.getFriendDetail(_friend.wxId);
        if (detailResponse.isSuccess && detailResponse.data != null) {
          setState(() {
            _friend = detailResponse.data!;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('好友信息已更新')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新失败: ${response.message}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('刷新失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 开始聊天
  void _startChat() {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': _friend.wxId,
        'contactName': _friend.name,
        'isGroup': false,
      },
    );
  }

  /// 复制微信号
  void _copyWxId() {
    Clipboard.setData(ClipboardData(text: _friend.wxId));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('微信号已复制到剪贴板')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('好友详情'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshFriendInfo,
            tooltip: '刷新信息',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(),
            _buildInfoSection(),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundImage: _friend.avatarUrl != null 
                ? NetworkImage(_friend.avatarUrl!) 
                : null,
            backgroundColor: Colors.grey[300],
            child: _friend.avatarUrl == null 
                ? Text(
                    _getAvatarText(),
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          const SizedBox(height: 16),
          Text(
            _friend.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (_friend.signature?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Text(
              _friend.signature!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildInfoItem(
            icon: Icons.account_circle,
            title: '微信号',
            content: _friend.wxId,
            onTap: _copyWxId,
            showCopy: true,
          ),
          if (_friend.alias?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.alternate_email,
              title: '微信别名',
              content: _friend.alias!,
            ),
          if (_friend.remark?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.edit,
              title: '备注',
              content: _friend.remark!,
            ),
          if (_friend.sex != null && _friend.sex! > 0)
            _buildInfoItem(
              icon: _friend.sex == 1 ? Icons.male : Icons.female,
              title: '性别',
              content: _friend.sexDescription,
            ),
          if (_friend.province?.isNotEmpty == true || _friend.city?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.location_on,
              title: '地区',
              content: [_friend.province, _friend.city]
                  .where((s) => s?.isNotEmpty == true)
                  .join(' '),
            ),
          if (_friend.country?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.public,
              title: '国家',
              content: _friend.country!,
            ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String content,
    VoidCallback? onTap,
    bool showCopy = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(content),
      trailing: showCopy 
          ? IconButton(
              icon: const Icon(Icons.copy, size: 20),
              onPressed: onTap,
              tooltip: '复制',
            )
          : null,
      onTap: onTap,
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _startChat,
              icon: const Icon(Icons.chat),
              label: const Text('发送消息'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: 实现查看朋友圈功能
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('朋友圈功能开发中...')),
                    );
                  },
                  icon: const Icon(Icons.photo_library),
                  label: const Text('朋友圈'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: 实现更多功能
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('更多功能开发中...')),
                    );
                  },
                  icon: const Icon(Icons.more_horiz),
                  label: const Text('更多'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getAvatarText() {
    final name = _friend.name;
    if (name.isEmpty) return '?';
    
    // 如果是中文，取最后一个字符
    if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(name)) {
      return name.substring(name.length - 1);
    }
    
    // 如果是英文，取第一个字符
    return name.substring(0, 1).toUpperCase();
  }
}
