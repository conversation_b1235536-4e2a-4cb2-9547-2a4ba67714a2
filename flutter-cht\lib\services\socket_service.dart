import 'dart:async';
import 'dart:developer' as developer;

import 'package:socket_io_client/socket_io_client.dart' as IO;
import '../constants/socket_events.dart';
import '../models/chat_message.dart';
import '../models/system_message.dart';
import '../models/user_info.dart';

/// Socket.IO 服务类
/// 负责与后端 Socket.IO 服务器的连接和通信
class SocketService {
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;
  SocketService._internal();

  IO.Socket? _socket;
  UserInfo? _currentUser;
  bool _isConnected = false;

  // 事件流控制器
  final StreamController<ChatMessage> _groupMessageController =
      StreamController<ChatMessage>.broadcast();
  final StreamController<ChatMessage> _privateMessageController =
      StreamController<ChatMessage>.broadcast();
  final StreamController<SystemMessage> _systemMessageController =
      StreamController<SystemMessage>.broadcast();
  final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();

  // 公开的事件流
  Stream<ChatMessage> get groupMessageStream => _groupMessageController.stream;
  Stream<ChatMessage> get privateMessageStream =>
      _privateMessageController.stream;
  Stream<SystemMessage> get systemMessageStream =>
      _systemMessageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;

  bool get isConnected => _isConnected;
  UserInfo? get currentUser => _currentUser;

  /// 连接到 Socket.IO 服务器
  Future<void> connect({
    required String serverUrl,
    required String jwtToken,
    required UserInfo userInfo,
  }) async {
    try {
      _currentUser = userInfo;

      // 配置 Socket.IO 选项
      final options =
          IO.OptionBuilder()
              .setTransports(['websocket', 'polling']) // 支持 WebSocket 和轮询
              .setExtraHeaders({
                'Authorization': 'Bearer $jwtToken', // JWT 认证
              })
              .enableAutoConnect() // 自动连接
              .enableReconnection() // 启用重连
              .setReconnectionAttempts(5) // 重连尝试次数
              .setReconnectionDelay(1000) // 重连延迟
              .build();

      // 创建 Socket 连接
      _socket = IO.io(serverUrl, options);

      // 设置连接事件监听器
      _setupConnectionListeners();

      // 设置消息事件监听器
      _setupMessageListeners();

      // 连接到服务器
      _socket!.connect();

      developer.log('🚀 开始连接 Socket.IO 服务器: $serverUrl');
    } catch (e) {
      developer.log('❌ Socket.IO 连接失败: $e');
      rethrow;
    }
  }

  /// 设置连接事件监听器
  void _setupConnectionListeners() {
    _socket!.onConnect((_) {
      _isConnected = true;
      _connectionController.add(true);
      developer.log('✅ Socket.IO 连接成功');
    });

    _socket!.onDisconnect((_) {
      _isConnected = false;
      _connectionController.add(false);
      developer.log('❌ Socket.IO 连接断开');
    });

    _socket!.onConnectError((error) {
      _isConnected = false;
      _connectionController.add(false);
      developer.log('❌ Socket.IO 连接错误: $error');
    });

    _socket!.onReconnect((_) {
      developer.log('🔄 Socket.IO 重新连接成功');
    });

    _socket!.onReconnectError((error) {
      developer.log('❌ Socket.IO 重连失败: $error');
    });
  }

  /// 设置消息事件监听器
  void _setupMessageListeners() {
    // 群聊消息
    _socket!.on(SocketEvents.groupMessage, (data) {
      try {
        final message = ChatMessage.fromJson(data);
        _groupMessageController.add(message);
        developer.log('📩 收到群聊消息: ${message.content}');
      } catch (e) {
        developer.log('❌ 解析群聊消息失败: $e');
      }
    });

    // 私聊消息
    _socket!.on(SocketEvents.privateMessage, (data) {
      try {
        final message = ChatMessage.fromJson(data);
        _privateMessageController.add(message);
        developer.log('📩 收到私聊消息: ${message.content}');
      } catch (e) {
        developer.log('❌ 解析私聊消息失败: $e');
      }
    });

    // 用户上线通知
    _socket!.on(SocketEvents.userOnline, (data) {
      try {
        final systemMessage = SystemMessage.fromJson(data);
        _systemMessageController.add(systemMessage);
        developer.log('✅ 用户上线: ${systemMessage.userId}');
      } catch (e) {
        developer.log('❌ 解析用户上线消息失败: $e');
      }
    });

    // 用户下线通知
    _socket!.on(SocketEvents.userOffline, (data) {
      try {
        final systemMessage = SystemMessage.fromJson(data);
        _systemMessageController.add(systemMessage);
        developer.log('❌ 用户下线: ${systemMessage.userId}');
      } catch (e) {
        developer.log('❌ 解析用户下线消息失败: $e');
      }
    });

    // 强制断开连接
    _socket!.on(SocketEvents.forceDisconnect, (data) {
      try {
        final systemMessage = SystemMessage.fromJson(data);
        _systemMessageController.add(systemMessage);
        developer.log('⚠️ 强制断开连接: ${systemMessage.content}');
        disconnect();
      } catch (e) {
        developer.log('❌ 解析强制断开消息失败: $e');
      }
    });

    // 系统广播
    _socket!.on(SocketEvents.systemBroadcast, (data) {
      try {
        final systemMessage = SystemMessage.fromJson(data);
        _systemMessageController.add(systemMessage);
        developer.log('📢 系统广播: ${systemMessage.content}');
      } catch (e) {
        developer.log('❌ 解析系统广播失败: $e');
      }
    });
  }

  /// 发送群聊消息
  void sendGroupMessage({required String groupId, required String content}) {
    if (!_isConnected || _currentUser == null) {
      developer.log('❌ 无法发送群聊消息: 未连接或用户信息缺失');
      return;
    }

    final message = ChatMessage.groupMessage(
      tenantId: _currentUser!.tenantId!,
      fromUserId: _currentUser!.id!,
      groupId: groupId,
      content: content,
    );

    _socket!.emit(SocketEvents.groupMessage, message.toJson());
    developer.log('📤 发送群聊消息: $content');
  }

  /// 发送私聊消息
  void sendPrivateMessage({required String toUserId, required String content}) {
    if (!_isConnected || _currentUser == null) {
      developer.log('❌ 无法发送私聊消息: 未连接或用户信息缺失');
      return;
    }

    final message = ChatMessage.privateMessage(
      tenantId: _currentUser!.tenantId!,
      fromUserId: _currentUser!.id!,
      toUserId: toUserId,
      content: content,
    );

    _socket!.emit(SocketEvents.privateMessage, message.toJson());
    developer.log('📤 发送私聊消息: $content');
  }

  /// 断开连接
  void disconnect() {
    _socket?.disconnect();
    _socket?.dispose();
    _socket = null;
    _currentUser = null;
    _isConnected = false;
    developer.log('🔌 Socket.IO 连接已断开');
  }

  /// 释放资源
  void dispose() {
    disconnect();
    _groupMessageController.close();
    _privateMessageController.close();
    _systemMessageController.close();
    _connectionController.close();
  }
}
