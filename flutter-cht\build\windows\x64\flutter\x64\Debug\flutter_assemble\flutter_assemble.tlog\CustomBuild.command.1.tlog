^E:\ME\FLUTTER-CHT\BUILD\WINDOWS\X64\CMAKEFILES\A81D73CDE3BDFE8163CEC46D0C337B08\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=E:\software\flutter PROJECT_DIR=E:\me\flutter-cht FLUTTER_ROOT=E:\software\flutter FLUTTER_EPHEMERAL_DIR=E:\me\flutter-cht\windows\flutter\ephemeral PROJECT_DIR=E:\me\flutter-cht FLUTTER_TARGET=E:\me\flutter-cht\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=E:\me\flutter-cht\.dart_tool\package_config.json E:/software/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\ME\FLUTTER-CHT\BUILD\WINDOWS\X64\CMAKEFILES\132D045A13C468D80F882C8ECFE82FC7\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\ME\FLUTTER-CHT\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/me/flutter-cht/windows -BE:/me/flutter-cht/build/windows/x64 --check-stamp-file E:/me/flutter-cht/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
