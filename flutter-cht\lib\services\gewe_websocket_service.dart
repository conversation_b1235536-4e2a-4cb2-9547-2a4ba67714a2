import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gewe_message.dart';
import '../models/gewe_message_callback.dart';
import '../network/websocket_client.dart';

/// GeWe WebSocket 消息处理服务
class GeWeWebSocketService {
  static GeWeWebSocketService? _instance;

  // 消息流控制器
  final StreamController<GeWeMessage> _messageController =
      StreamController.broadcast();
  final StreamController<MessageCallbackResult> _callbackController =
      StreamController.broadcast();

  // WebSocket 客户端
  final WebSocketClient _wsClient = WebSocketClient.instance;

  // 订阅
  StreamSubscription? _wsMessageSubscription;
  StreamSubscription? _wsStatusSubscription;

  GeWeWebSocketService._internal() {
    _initializeWebSocketListeners();
  }

  static GeWeWebSocketService get instance {
    _instance ??= GeWeWebSocketService._internal();
    return _instance!;
  }

  /// 消息流 - 解析后的 GeWe 消息
  Stream<GeWeMessage> get messageStream => _messageController.stream;

  /// 回调处理结果流
  Stream<MessageCallbackResult> get callbackResultStream =>
      _callbackController.stream;

  /// WebSocket 连接状态流
  Stream<WebSocketStatus> get connectionStatusStream => _wsClient.statusStream;

  /// 当前连接状态
  WebSocketStatus get connectionStatus => _wsClient.status;

  /// 是否已连接
  bool get isConnected => _wsClient.isConnected;

  /// 初始化 WebSocket 监听器
  void _initializeWebSocketListeners() {
    // 监听 WebSocket 消息
    _wsMessageSubscription = _wsClient.messageStream.listen(
      _handleWebSocketMessage,
      onError: (error) {
        if (kDebugMode) {
          print('🔌 WebSocket message stream error: $error');
        }
      },
    );

    // 监听 WebSocket 连接状态
    _wsStatusSubscription = _wsClient.statusStream.listen(
      _handleConnectionStatusChange,
      onError: (error) {
        if (kDebugMode) {
          print('🔌 WebSocket status stream error: $error');
        }
      },
    );
  }

  /// 处理 WebSocket 消息
  void _handleWebSocketMessage(WebSocketMessage wsMessage) {
    try {
      if (kDebugMode) {
        print('📨 Received WebSocket message: ${wsMessage.type}');
      }

      // 检查是否为 GeWe 消息回调
      if (wsMessage.type == 'gewe_message_callback' ||
          wsMessage.type == 'message_callback') {
        _handleGeWeMessageCallback(wsMessage.data);
      } else if (wsMessage.type == 'gewe_message') {
        _handleDirectGeWeMessage(wsMessage.data);
      } else {
        if (kDebugMode) {
          print('📨 Unknown message type: ${wsMessage.type}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling WebSocket message: $e');
      }
    }
  }

  /// 处理 GeWe 消息回调
  void _handleGeWeMessageCallback(Map<String, dynamic> data) {
    try {
      final callback = GeWeMessageCallback.fromJson(data);
      final result = _parseMessageCallback(callback);

      _callbackController.add(result);

      if (result.success && result.parsedMessage != null) {
        _messageController.add(result.parsedMessage!);

        if (kDebugMode) {
          print('✅ Parsed GeWe message: ${result.parsedMessage}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error parsing GeWe message callback: $e');
      }

      _callbackController.add(
        MessageCallbackResult.failure(
          message: 'Failed to parse message callback: $e',
        ),
      );
    }
  }

  /// 处理直接的 GeWe 消息
  void _handleDirectGeWeMessage(Map<String, dynamic> data) {
    try {
      // 兼容后端 Spring WebSocket 推送格式
      final adapted = _adaptBackendPushToGeWeMessage(data);
      _messageController.add(adapted);

      if (kDebugMode) {
        print('✅ Received direct GeWe message: $adapted');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error parsing direct GeWe message: $e');
      }
    }
  }

  GeWeMessage _adaptBackendPushToGeWeMessage(Map<String, dynamic> data) {
    // 预期字段：fromWxId, toWxId, messageType(字符串), content, atWxIds(数组), sendTime/receiveTime(秒)
    final String? fromWxId = data['fromWxId'] as String?;
    final String? toWxId = data['toWxId'] as String?;
    final dynamic mt = data['messageType'];
    final String? content = data['content'] as String?;
    final dynamic atWxIds = data['atWxIds'];
    final dynamic sendTime = data['sendTime'];
    final dynamic receiveTime = data['receiveTime'];

    final int? msgTypeInt = _mapMessageTypeToInt(mt);
    final DateTime? sendDt = _dateTimeFromSeconds(sendTime);
    final DateTime? recvDt = _dateTimeFromSeconds(receiveTime);

    String? atJoined;
    if (atWxIds is List) {
      atJoined = atWxIds
          .map((e) => e?.toString() ?? '')
          .where((e) => e.isNotEmpty)
          .join(',');
    } else if (atWxIds is String) {
      atJoined = atWxIds;
    }

    final bool isGroup =
        (toWxId?.endsWith('@chatroom') == true) ||
        (toWxId?.startsWith('@@') == true);

    return GeWeMessage(
      msgId: null,
      fromWxId: fromWxId,
      toWxId: toWxId,
      content: content,
      msgType: msgTypeInt,
      sendTime: sendDt,
      receiveTime: recvDt,
      isGroup: isGroup,
      isSent: false,
      atWxIds: atJoined,
    );
  }

  int? _mapMessageTypeToInt(dynamic mt) {
    if (mt == null) return null;
    if (mt is num) return mt.toInt();
    final s = mt.toString().toLowerCase();
    switch (s) {
      case 'text':
        return 1;
      case 'image':
        return 3;
      case 'voice':
        return 34;
      case 'video':
        return 43;
      case 'file':
        return 49;
      case 'location':
        return 48;
      case 'emoji':
        return 47;
      case 'system':
        return 10000;
      default:
        return null;
    }
  }

  DateTime? _dateTimeFromSeconds(dynamic v) {
    if (v == null) return null;
    try {
      final int seconds = v is num ? v.toInt() : int.parse(v.toString());
      return DateTime.fromMillisecondsSinceEpoch(seconds * 1000, isUtc: false);
    } catch (_) {
      return null;
    }
  }

  /// 解析消息回调
  MessageCallbackResult _parseMessageCallback(GeWeMessageCallback callback) {
    try {
      final eventType = CallbackEventType.fromValue(callback.typeName);

      if (eventType == CallbackEventType.addMsg) {
        // 解析新消息
        final message = GeWeMessage(
          msgId: callback.msgId,
          fromWxId: callback.fromWxId,
          toWxId: callback.toWxId,
          content: callback.content,
          msgType: callback.msgType,
          createTime: callback.createTime,
          isGroup: callback.toWxId?.endsWith('@chatroom'),
          isSent: false, // 接收到的消息
          pushContent: callback.data?.pushContent,
        );

        return MessageCallbackResult.success(
          message: message,
          eventType: eventType,
        );
      } else {
        return MessageCallbackResult.failure(
          message: 'Unsupported event type: ${callback.typeName}',
          eventType: eventType,
        );
      }
    } catch (e) {
      return MessageCallbackResult.failure(
        message: 'Failed to parse callback: $e',
      );
    }
  }

  /// 处理连接状态变化
  void _handleConnectionStatusChange(WebSocketStatus status) {
    if (kDebugMode) {
      print('🔌 WebSocket status changed: $status');
    }

    switch (status) {
      case WebSocketStatus.connected:
        _onConnected();
        break;
      case WebSocketStatus.disconnected:
        _onDisconnected();
        break;
      case WebSocketStatus.error:
        _onError();
        break;
      default:
        break;
    }
  }

  /// 连接成功处理
  void _onConnected() {
    if (kDebugMode) {
      print('✅ GeWe WebSocket service connected');
    }

    // 可以在这里发送订阅消息或心跳
    _subscribeToGeWeMessages();
  }

  /// 连接断开处理
  void _onDisconnected() {
    if (kDebugMode) {
      print('❌ GeWe WebSocket service disconnected');
    }
  }

  /// 连接错误处理
  void _onError() {
    if (kDebugMode) {
      print('❌ GeWe WebSocket service error');
    }
  }

  /// 订阅 GeWe 消息
  void _subscribeToGeWeMessages() {
    final subscribeMessage = WebSocketMessage(
      type: 'subscribe',
      data: {
        'channel': 'gewe_messages',
        'events': ['message_callback', 'gewe_message'],
      },
    );

    _wsClient.sendMessage(subscribeMessage);

    if (kDebugMode) {
      print('📡 Subscribed to GeWe messages');
    }
  }

  /// 连接 WebSocket
  Future<void> connect({
    String? token,
    required String currentUserWxId,
    String? chatroomId,
  }) async {
    await _wsClient.connect(
      token: token,
      wxId: currentUserWxId,
      chatroomId: chatroomId,
    );
  }

  /// 断开 WebSocket 连接
  Future<void> disconnect() async {
    await _wsClient.disconnect();
  }

  /// 发送消息到 WebSocket
  void sendWebSocketMessage(WebSocketMessage message) {
    _wsClient.sendMessage(message);
  }

  /// 释放资源
  void dispose() {
    _wsMessageSubscription?.cancel();
    _wsStatusSubscription?.cancel();
    _messageController.close();
    _callbackController.close();
    _wsClient.dispose();
  }
}
