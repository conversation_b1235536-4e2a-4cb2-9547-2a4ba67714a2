import 'dart:convert';
import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_info.dart';

/// Socket.IO 认证管理器
/// 专门负责Socket.IO连接所需的JWT认证
class SocketAuthManager {
  static final SocketAuthManager _instance = SocketAuthManager._internal();
  factory SocketAuthManager() => _instance;
  SocketAuthManager._internal();

  final Dio _dio = Dio();
  UserInfo? _currentUser;

  static const String _tokenKey = 'socket_jwt_token';
  static const String _userInfoKey = 'socket_user_info';

  UserInfo? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null && _currentUser!.token != null;

  /// 初始化认证管理器
  Future<void> init({required String baseUrl}) async {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);

    // 添加请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (_currentUser?.token != null) {
            options.headers['Authorization'] = 'Bearer ${_currentUser!.token}';
          }
          developer.log('🌐 Socket认证API请求: ${options.method} ${options.path}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          developer.log(
            '✅ Socket认证API响应: ${response.statusCode} ${response.requestOptions.path}',
          );
          handler.next(response);
        },
        onError: (error, handler) {
          developer.log(
            '❌ Socket认证API错误: ${error.response?.statusCode} ${error.message}',
          );
          handler.next(error);
        },
      ),
    );

    // 尝试从本地存储恢复用户信息
    await _loadUserFromStorage();
  }

  /// 用户登录（专门为Socket.IO连接）
  Future<UserInfo?> login({
    required String phone,
    required String tenantId,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        '/auth/login',
        data: {
          'phone': phone,
          'tenantId': int.parse(tenantId),
          'password': password,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Socket.IO Flutter Client/1.0.0',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );

      if (response.statusCode == 200 && response.data['code'] == 200) {
        final data = response.data['data'];
        final token = data['token'] as String;

        // 解析JWT token获取用户信息
        final userInfo = _parseJwtToken(token);
        if (userInfo != null) {
          _currentUser = userInfo.copyWith(token: token);
          await _saveUserToStorage(_currentUser!);
          developer.log('✅ Socket认证登录成功: ${_currentUser!.username}');
          return _currentUser;
        }
      }

      developer.log('❌ Socket认证登录失败: ${response.data['message']}');
      return null;
    } catch (e) {
      developer.log('❌ Socket认证登录异常: $e');
      return null;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _currentUser = null;
    await _clearUserFromStorage();
    developer.log('👋 Socket认证用户已登出');
  }

  /// 获取当前有效的JWT token
  String? getValidToken() {
    if (_currentUser?.token == null) return null;

    if (_isTokenValid(_currentUser!.token!)) {
      return _currentUser!.token;
    } else {
      // Token已过期，清除用户信息
      logout();
      return null;
    }
  }

  /// 解析JWT token获取用户信息
  UserInfo? _parseJwtToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      // 添加必要的填充
      final normalizedPayload = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final claims = json.decode(decoded) as Map<String, dynamic>;

      return UserInfo(
        id: claims['id']?.toString(),
        tenantId: claims['tenantId']?.toString(),
        username: claims['sub'] as String?,
        phone: claims['phone'] as String?,
        token: token,
      );
    } catch (e) {
      developer.log('❌ 解析JWT token失败: $e');
      return null;
    }
  }

  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage(UserInfo userInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, userInfo.token ?? '');
      await prefs.setString(_userInfoKey, json.encode(userInfo.toJson()));
    } catch (e) {
      developer.log('❌ 保存Socket用户信息失败: $e');
    }
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);
      final userInfoJson = prefs.getString(_userInfoKey);

      if (token != null && userInfoJson != null) {
        final userInfo = UserInfo.fromJson(json.decode(userInfoJson));
        if (_isTokenValid(token)) {
          _currentUser = userInfo;
          developer.log('✅ 从本地存储恢复Socket用户信息: ${_currentUser!.username}');
        } else {
          await _clearUserFromStorage();
          developer.log('⚠️ Socket Token已过期，清除本地存储');
        }
      }
    } catch (e) {
      developer.log('❌ 加载Socket用户信息失败: $e');
    }
  }

  /// 清除本地存储的用户信息
  Future<void> _clearUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userInfoKey);
    } catch (e) {
      developer.log('❌ 清除Socket用户信息失败: $e');
    }
  }

  /// 检查token是否有效（简单的过期时间检查）
  bool _isTokenValid(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return false;

      final payload = parts[1];
      final normalizedPayload = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final claims = json.decode(decoded) as Map<String, dynamic>;

      final exp = claims['exp'] as int?;
      if (exp == null) return false;

      final expirationTime = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      return DateTime.now().isBefore(expirationTime);
    } catch (e) {
      return false;
    }
  }
}

/// UserInfo 扩展方法
extension UserInfoExtension on UserInfo {
  UserInfo copyWith({
    String? id,
    String? tenantId,
    String? username,
    String? phone,
    String? token,
  }) {
    return UserInfo(
      id: id ?? this.id,
      tenantId: tenantId ?? this.tenantId,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      token: token ?? this.token,
    );
  }
}
