import '../models/api_response.dart';
import '../network/base_service.dart';

class LoginService extends BaseService {
  Future<ApiResponse<LoginResult>> login({
    required String phone,
    required String password,
    required int tenantId,
  }) async {
    return post<LoginResult>(
      '/auth/login',
      data: {
        'phone': phone,
        'password': password,
        'tenantId': tenantId,
      },
      fromJson: (json) => LoginResult.fromJson(json as Map<String, dynamic>),
    );
  }
}

class LoginResult {
  final String token;
  LoginResult({required this.token});
  factory LoginResult.fromJson(Map<String, dynamic> json) =>
      LoginResult(token: json['token'] as String);
}

