import '../models/api_response.dart';
import 'dio_client.dart';

/// 网络服务基类
abstract class BaseService {
  final DioClient _dioClient = DioClient.instance;

  /// GET 请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) {
    return _dioClient.get<T>(
      path,
      queryParameters: queryParameters,
      fromJson: fromJson,
    );
  }

  /// POST 请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) {
    return _dioClient.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      fromJson: fromJson,
    );
  }

  /// PUT 请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) {
    return _dioClient.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      fromJson: fromJson,
    );
  }

  /// DELETE 请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) {
    return _dioClient.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      fromJson: fromJson,
    );
  }

  /// 文件上传
  Future<ApiResponse<T>> upload<T>(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    Function(int, int)? onProgress,
    T Function(dynamic)? fromJson,
  }) {
    return _dioClient.upload<T>(
      path,
      filePath,
      fileName: fileName,
      data: data,
      onSendProgress: onProgress,
      fromJson: fromJson,
    );
  }

  /// 分页请求
  Future<ApiResponse<PageResponse<T>>> getPage<T>(
    String path, {
    int page = 1,
    int size = 20,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) {
    final params = {
      'current': page,
      'size': size,
      ...?queryParameters,
    };

    return _dioClient.get<PageResponse<T>>(
      path,
      queryParameters: params,
      fromJson: (json) => PageResponse<T>.fromJson(
        json as Map<String, dynamic>,
        (item) => fromJson!(item),
      ),
    );
  }
}
