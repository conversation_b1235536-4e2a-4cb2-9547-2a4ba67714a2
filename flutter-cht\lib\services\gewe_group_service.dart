import '../models/api_response.dart';
import '../models/gewe_message.dart';
import '../network/base_service.dart';

class GeWeGroupService extends BaseService {
  Future<ApiResponse<List<GroupListItem>>> listGroups({String? wxId}) async {
    return await get<List<GroupListItem>>(
      wxId == null || wxId.isEmpty
          ? '/gewe/group/list'
          : '/gewe/group/listByWxId',
      queryParameters: wxId == null || wxId.isEmpty ? null : {'wxId': wxId},
      fromJson:
          (json) =>
              (json as List)
                  .map((e) => GroupListItem.fromJson(e as Map<String, dynamic>))
                  .toList(),
    );
  }

  Future<ApiResponse<GroupInfo>> getChatroomInfo(String chatroomId) async {
    return await post<GroupInfo>(
      '/gewe/group/getChatroomInfo',
      data: {'chatroomId': chatroomId},
      fromJson: (json) => GroupInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<ApiResponse<MemberListData>> getChatroomMemberList(
    String chatroomId,
  ) async {
    return await post<MemberListData>(
      '/gewe/group/getChatroomMemberList',
      data: {'chatroomId': chatroomId},
      fromJson: (json) => MemberListData.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<ApiResponse<PageResponse<GeWeMessage>>> getGroupMessages(
    String chatroomId, {
    int page = 0,
    int size = 20,
  }) async {
    return await get<PageResponse<GeWeMessage>>(
      '/gewe/group/message/history',
      queryParameters: {'chatroomId': chatroomId, 'page': page, 'size': size},
      fromJson:
          (json) => PageResponse<GeWeMessage>.fromJson(
            json as Map<String, dynamic>,
            (item) => GeWeMessage.fromJson(item as Map<String, dynamic>),
          ),
    );
  }

  Future<ApiResponse<List<MemberDetail>>> getChatroomMemberDetail(
    String chatroomId,
    List<String> memberWxids,
  ) async {
    return await post<List<MemberDetail>>(
      '/gewe/group/getChatroomMemberDetail',
      data: {'chatroomId': chatroomId, 'memberWxids': memberWxids},
      fromJson:
          (json) =>
              (json as List)
                  .map((e) => MemberDetail.fromJson(e as Map<String, dynamic>))
                  .toList(),
    );
  }
}

class GroupListItem {
  final String chatroomId;
  final String? nickName;
  final String? ownerWxid;
  final String? smallHeadImgUrl;
  final String? remark;

  GroupListItem({
    required this.chatroomId,
    this.nickName,
    this.ownerWxid,
    this.smallHeadImgUrl,
    this.remark,
  });

  factory GroupListItem.fromJson(Map<String, dynamic> json) => GroupListItem(
    chatroomId: json['chatroomId'] as String,
    nickName: json['nickName'] as String?,
    ownerWxid: json['ownerWxid'] as String?,
    smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
    remark: json['remark'] as String?,
  );
}

class GroupInfo {
  final String chatroomId;
  final String? nickName;
  final String? ownerWxid;
  final String? smallHeadImgUrl;
  final int memberCount;

  GroupInfo({
    required this.chatroomId,
    this.nickName,
    this.ownerWxid,
    this.smallHeadImgUrl,
    required this.memberCount,
  });

  factory GroupInfo.fromJson(Map<String, dynamic> json) => GroupInfo(
    chatroomId: json['chatroomId'] as String,
    nickName: json['nickName'] as String?,
    ownerWxid: json['ownerWxid'] as String?,
    smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
    memberCount: (json['memberCount'] as num?)?.toInt() ?? 0,
  );
}

class MemberListData {
  final List<GroupMember> memberList;
  final String? chatroomOwner;
  final List<String> adminWxid;

  MemberListData({
    required this.memberList,
    this.chatroomOwner,
    required this.adminWxid,
  });

  factory MemberListData.fromJson(Map<String, dynamic> json) => MemberListData(
    memberList:
        ((json['memberList'] as List?) ?? [])
            .map((e) => GroupMember.fromJson(e as Map<String, dynamic>))
            .toList(),
    chatroomOwner: json['chatroomOwner'] as String?,
    adminWxid:
        ((json['adminWxid'] as List?) ?? []).map((e) => e.toString()).toList(),
  );
}

class GroupMember {
  final String wxid;
  final String? nickName;
  final String? displayName;
  final String? inviterUserName;
  final int? memberFlag;
  final String? bigHeadImgUrl;
  final String? smallHeadImgUrl;

  GroupMember({
    required this.wxid,
    this.nickName,
    this.displayName,
    this.inviterUserName,
    this.memberFlag,
    this.bigHeadImgUrl,
    this.smallHeadImgUrl,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) => GroupMember(
    wxid: json['wxid'] as String,
    nickName: json['nickName'] as String?,
    displayName: json['displayName'] as String?,
    inviterUserName: json['inviterUserName'] as String?,
    memberFlag: (json['memberFlag'] as num?)?.toInt(),
    bigHeadImgUrl: json['bigHeadImgUrl'] as String?,
    smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
  );
}

class MemberDetail {
  final String userName;
  final String? nickName;
  final String? alias;
  final String? bigHeadImgUrl;
  final String? smallHeadImgUrl;
  final String? country;
  final String? province;
  final String? city;
  final String? signature;
  final int? sex;
  final int? memberFlag;

  MemberDetail({
    required this.userName,
    this.nickName,
    this.alias,
    this.bigHeadImgUrl,
    this.smallHeadImgUrl,
    this.country,
    this.province,
    this.city,
    this.signature,
    this.sex,
    this.memberFlag,
  });

  factory MemberDetail.fromJson(Map<String, dynamic> json) => MemberDetail(
    userName: json['userName'] as String,
    nickName: json['nickName'] as String?,
    alias: json['alias'] as String?,
    bigHeadImgUrl: json['bigHeadImgUrl'] as String?,
    smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
    country: json['country'] as String?,
    province: json['province'] as String?,
    city: json['city'] as String?,
    signature: json['signature'] as String?,
    sex: (json['sex'] as num?)?.toInt(),
    memberFlag: (json['memberFlag'] as num?)?.toInt(),
  );
}
