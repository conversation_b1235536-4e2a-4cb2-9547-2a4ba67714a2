package com.tourism.chat.tenant;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.stereotype.Component;

/**
 * Tenant handler for MyBatis-Plus tenant line interceptor.
 */
@Component
public class MyTenantHandler implements TenantLineHandler {

    @Override
    public Expression getTenantId() {
        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId == null) {
            // Default to -1 when missing; could also throw exception depending on policy
            return new LongValue(-1L);
        }
        return new LongValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // return true to ignore specific tables from tenant filtering
        if (TenantContextHolder.getContext().getIgnoreTenant()) {
             return true;
        }
        if ("user".equals(tableName)) {
            return true;
        }
        return false;
    }
}

