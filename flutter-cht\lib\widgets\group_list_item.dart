import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_group_service.dart' show GroupListItem;

/// 群聊列表项组件
class GroupTile extends StatelessWidget {
  final GroupListItem group;
  final VoidCallback? onTap;
  final VoidCallback? onChatTap;
  final bool showChatButton;

  const GroupTile({
    super.key,
    required this.group,
    this.onTap,
    this.onChatTap,
    this.showChatButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: _buildAvatar(),
      title: _buildTitle(),
      subtitle: _buildSubtitle(),
      trailing: showChatButton ? _buildTrailing(context) : null,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 24,
      backgroundImage:
          group.smallHeadImgUrl != null
              ? NetworkImage(group.smallHeadImgUrl!)
              : null,
      backgroundColor: Colors.green[300],
      child:
          group.smallHeadImgUrl == null
              ? const Icon(Icons.group, color: Colors.white, size: 24)
              : null,
    );
  }

  Widget _buildTitle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            group.nickName ?? group.remark ?? group.chatroomId,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Container(
          margin: const EdgeInsets.only(left: 8),
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.green[100],
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Text(
            '群聊',
            style: TextStyle(
              fontSize: 10,
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubtitle() {
    final List<String> subtitleParts = [];

    // 添加群微信号
    subtitleParts.add('群号: ${group.chatroomId}');

    // 添加群描述（如果有）
    if (group.remark?.isNotEmpty == true) {
      subtitleParts.add(group.remark!);
    }

    return Text(
      subtitleParts.join(' • '),
      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget? _buildTrailing(BuildContext context) {
    if (!showChatButton) return null;

    return IconButton(
      icon: const Icon(Icons.chat_bubble_outline),
      onPressed: onChatTap,
      tooltip: '进入群聊',
      iconSize: 20,
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
    );
  }
}
