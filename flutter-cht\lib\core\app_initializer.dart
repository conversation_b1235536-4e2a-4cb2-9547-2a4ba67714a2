import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../network/dio_client.dart';
import '../network/websocket_client.dart';
// import '../services/gewe_service_manager.dart';
// import 'message_dispatcher.dart';
import 'logger.dart';
import '../storage/token_storage.dart';

/// 应用初始化器
class AppInitializer {
  static bool _isInitialized = false;

  /// 初始化应用
  static Future<void> initialize({String? currentUserWxId}) async {
    if (_isInitialized) return;

    Logger.i('🚀 Initializing IM App...');

    // 初始化网络客户端
    await _initializeNetworkClient();

    // 加载本地 Token 并注入到全局请求头
    final token = await TokenStorage.getToken();
    if (token != null && token.isNotEmpty) {
      DioClient.instance.setAuthToken(token);
      Logger.i('🔐 Loaded persisted token.');
    }

    // 初始化本地通知
    await _initializeNotifications();

    // 初始化 WebSocket 客户端
    _initializeWebSocket();

    // 初始化 GeWe 服务管理器
    // await _initializeGeWeServices(currentUserWxId);

    // 初始化消息分发器
    // _initializeMessageDispatcher();

    _isInitialized = true;

    Logger.i('✅ IM App initialized successfully');
  }

  /// 初始化网络客户端
  static Future<void> _initializeNetworkClient() async {
    try {
      // 初始化 DioClient 实例，这会自动配置拦截器
      DioClient.instance;

      Logger.i('✅ Network client initialized');
    } catch (e) {
      Logger.e('❌ Failed to initialize network client', e);
      rethrow;
    }
  }

  /// 初始化本地通知
  static Future<void> _initializeNotifications() async {
    try {
      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // Android 初始化设置
      const androidInitializationSettings = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );

      // iOS 初始化设置
      const iosInitializationSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // 初始化设置
      const initializationSettings = InitializationSettings(
        android: androidInitializationSettings,
        iOS: iosInitializationSettings,
      );

      // 初始化插件
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 请求 Android 13+ 通知权限
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();

      Logger.i('✅ Local notifications initialized');
    } catch (e) {
      Logger.e('❌ Failed to initialize notifications', e);
      // 通知初始化失败不应该阻止应用启动
    }
  }

  /// 初始化 WebSocket 客户端
  static void _initializeWebSocket() {
    try {
      // 获取 WebSocket 客户端实例
      final wsClient = WebSocketClient.instance;

      // 监听连接状态变化
      wsClient.statusStream.listen((status) {
        if (kDebugMode) {
          print('🔌 WebSocket status changed: $status');
        }
      });

      Logger.i('✅ WebSocket client initialized');
    } catch (e) {
      Logger.e('❌ Failed to initialize WebSocket client', e);
    }
  }

  // /// 初始化 GeWe 服务管理器
  // static Future<void> _initializeGeWeServices(String? currentUserWxId) async {
  //   try {
  //     final serviceManager = GeWeServiceManager.instance;
  //     await serviceManager.initialize(currentUserWxId: currentUserWxId);
  //
  //     if (kDebugMode) {
  //       print('✅ GeWe services initialized');
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('❌ Failed to initialize GeWe services: $e');
  //     }
  //     // GeWe 服务初始化失败不应该阻止应用启动
  //   }
  // }

  // /// 初始化消息分发器
  // static void _initializeMessageDispatcher() {
  //   try {
  //     final dispatcher = MessageDispatcher.instance;
  //
  //     // 配置消息缓存
  //     dispatcher.configureCaching(enabled: true, maxCacheSize: 100);
  //
  //     // 添加全局消息监听器用于调试
  //     if (kDebugMode) {
  //       dispatcher.addListener((event) {
  //         print('📨 Message event: ${event.type} - ${event.wxId}');
  //       });
  //     }
  //
  //     if (kDebugMode) {
  //       print('✅ Message dispatcher initialized');
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('❌ Failed to initialize message dispatcher: $e');
  //     }
  //   }
  // }

  /// 处理通知点击事件
  static void _onNotificationTapped(NotificationResponse response) {
    Logger.i('📱 Notification tapped: ${response.payload}');

    // TODO: 根据通知内容导航到相应页面
    // 例如：导航到聊天页面、好友请求页面等
  }

  /// 清理资源
  static Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      // 清理 GeWe 服务管理器
      // GeWeServiceManager.instance.dispose();

      // 清理消息分发器
      // MessageDispatcher.instance.dispose();

      // 断开 WebSocket 连接
      await WebSocketClient.instance.disconnect();

      Logger.i('✅ App resources disposed');
    } catch (e) {
      Logger.e('❌ Failed to dispose app resources', e);
    }

    _isInitialized = false;
  }
}
