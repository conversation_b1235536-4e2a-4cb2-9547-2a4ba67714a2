// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gewe_message_callback.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeWeMessageCallback _$GeWeMessageCallbackFromJson(Map<String, dynamic> json) =>
    GeWeMessageCallback(
      typeName: json['TypeName'] as String?,
      appid: json['Appid'] as String?,
      wxid: json['Wxid'] as String?,
      data:
          json['Data'] == null
              ? null
              : MessageCallbackData.fromJson(
                json['Data'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$GeWeMessageCallbackToJson(
  GeWeMessageCallback instance,
) => <String, dynamic>{
  'TypeName': instance.typeName,
  'Appid': instance.appid,
  'Wxid': instance.wxid,
  'Data': instance.data,
};

MessageCallbackData _$MessageCallbackDataFromJson(Map<String, dynamic> json) =>
    MessageCallbackData(
      msgId: json['MsgId'] as String?,
      fromUserName:
          json['FromUserName'] == null
              ? null
              : StringWrapper.fromJson(
                json['FromUserName'] as Map<String, dynamic>,
              ),
      toUserName:
          json['ToUserName'] == null
              ? null
              : StringWrapper.fromJson(
                json['ToUserName'] as Map<String, dynamic>,
              ),
      msgType: (json['MsgType'] as num?)?.toInt(),
      content:
          json['Content'] == null
              ? null
              : StringWrapper.fromJson(json['Content'] as Map<String, dynamic>),
      status: (json['Status'] as num?)?.toInt(),
      imgStatus: (json['ImgStatus'] as num?)?.toInt(),
      imgBuf:
          json['ImgBuf'] == null
              ? null
              : ImgBuf.fromJson(json['ImgBuf'] as Map<String, dynamic>),
      createTime:
          json['CreateTime'] == null
              ? null
              : DateTime.parse(json['CreateTime'] as String),
      msgSource: json['MsgSource'] as String?,
      pushContent: json['PushContent'] as String?,
      newMsgId: (json['NewMsgId'] as num?)?.toInt(),
      msgSeq: (json['MsgSeq'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MessageCallbackDataToJson(
  MessageCallbackData instance,
) => <String, dynamic>{
  'MsgId': instance.msgId,
  'FromUserName': instance.fromUserName,
  'ToUserName': instance.toUserName,
  'MsgType': instance.msgType,
  'Content': instance.content,
  'Status': instance.status,
  'ImgStatus': instance.imgStatus,
  'ImgBuf': instance.imgBuf,
  'CreateTime': instance.createTime?.toIso8601String(),
  'MsgSource': instance.msgSource,
  'PushContent': instance.pushContent,
  'NewMsgId': instance.newMsgId,
  'MsgSeq': instance.msgSeq,
};

StringWrapper _$StringWrapperFromJson(Map<String, dynamic> json) =>
    StringWrapper(string: json['string'] as String?);

Map<String, dynamic> _$StringWrapperToJson(StringWrapper instance) =>
    <String, dynamic>{'string': instance.string};

ImgBuf _$ImgBufFromJson(Map<String, dynamic> json) =>
    ImgBuf(iLen: (json['iLen'] as num?)?.toInt());

Map<String, dynamic> _$ImgBufToJson(ImgBuf instance) => <String, dynamic>{
  'iLen': instance.iLen,
};
