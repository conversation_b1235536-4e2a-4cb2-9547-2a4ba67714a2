<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1b7d2927-f461-41f7-93bd-6787aa1dc413" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/flutter-cht/lib/config/app_config.dart" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/lib/config/app_config.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugin_registrant.cc" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugin_registrant.cc" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugin_registrant.h" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugin_registrant.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugins.cmake" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/linux/flutter/generated_plugins.cmake" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/macos/Flutter/GeneratedPluginRegistrant.swift" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/macos/Flutter/GeneratedPluginRegistrant.swift" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugin_registrant.cc" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugin_registrant.cc" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugin_registrant.h" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugin_registrant.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugins.cmake" beforeDir="false" afterPath="$PROJECT_DIR$/flutter-cht/windows/flutter/generated_plugins.cmake" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tourism-chat/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/tourism-chat/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tourism-chat/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/tourism-chat/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/flutter-cht" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="325PRpb7NNm7PPBoGNlq9ZdXz33" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "E:/me",
    "settings.editor.selected.configurable": "flutter.settings",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1b7d2927-f461-41f7-93bd-6787aa1dc413" name="Changes" comment="" />
      <created>1756707454797</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756707454797</updated>
    </task>
    <servers />
  </component>
</project>