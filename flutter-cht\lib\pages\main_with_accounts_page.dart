import 'package:flutter/material.dart';
import '../models/wechat_account.dart';
import '../services/wechat_account_service.dart';
import '../services/gewe_service_manager.dart';
import '../widgets/accounts_sidebar.dart';
import 'friends_page.dart';
import 'groups_page.dart';
import 'recent_contacts_page.dart';
import 'settings_page.dart';
import '../storage/app_prefs.dart';

/// 主页面：左侧为账号列表侧栏，右侧为主 Tab 内容
class MainWithAccountsPage extends StatefulWidget {
  const MainWithAccountsPage({super.key});

  @override
  State<MainWithAccountsPage> createState() => _MainWithAccountsPageState();
}

class _MainWithAccountsPageState extends State<MainWithAccountsPage>
    with SingleTickerProviderStateMixin {
  final _accountService = WechatAccountService();
  final _serviceManager = GeWeServiceManager.instance;

  late TabController _tabController;
  int _currentIndex = 0;
  bool _loading = false;

  List<WechatAccount> _accounts = [];
  String? _selectedWxId;

  // 左侧账号侧栏开合与宽度
  bool _sidebarOpen = true;
  double _sidebarWidth = 140;
  static const double _sidebarCollapsedWidth = 44;

  final List<_NavTab> _tabs = const [
    _NavTab(label: '消息', icon: Icons.chat, page: RecentContactsPage()),
    _NavTab(label: '客户', icon: Icons.people, page: FriendsPage()),
    _NavTab(label: '群聊', icon: Icons.group, page: GroupsPage()),
    _NavTab(label: '设置', icon: Icons.settings, page: SettingsPage()),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() => _currentIndex = _tabController.index);
      }
    });
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() => _loading = true);
    final resp = await _accountService.getAccounts();
    if (!mounted) return;
    setState(() {
      _loading = false;
      _accounts = resp.data ?? [];
    });

    // 优先恢复上次选择的账号
    if (_accounts.isNotEmpty) {
      final saved = await AppPrefs.getSelectedWxId();
      final exists = _accounts.any((a) => a.wxId == saved);
      _selectedWxId = exists ? saved : _accounts.first.wxId;
      _serviceManager.setCurrentUserWxId(_selectedWxId!);
      _serviceManager.connect();
      await AppPrefs.setSelectedWxId(_selectedWxId!);
      if (mounted) setState(() {});
    }
  }

  void _onSelectAccount(WechatAccount a) async {
    setState(() {
      _selectedWxId = a.wxId;
    });
    _serviceManager.setCurrentUserWxId(a.wxId);
    _serviceManager.connect();
    await AppPrefs.setSelectedWxId(a.wxId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_tabs[_currentIndex].label),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAccounts,
            tooltip: '刷新账号列表',
          ),
        ],
      ),
      body: Row(
        children: [
          // 可折叠&可拖拽调整宽度的侧栏
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onHorizontalDragUpdate: (d) {
              setState(() {
                _sidebarWidth = (_sidebarWidth + d.delta.dx).clamp(220, 420);
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 180),
              width: _sidebarWidth,
              child: Stack(
                children: [
                  Positioned.fill(
                    child:
                        _sidebarWidth > 80
                            ? AccountsSidebar(
                              accounts: _accounts,
                              selectedWxId: _selectedWxId,
                              onSelect: _onSelectAccount,
                            )
                            : Container(
                              color: Colors.white,
                              child: ListView.builder(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                ),
                                itemCount: _accounts.length,
                                itemBuilder: (context, index) {
                                  final a = _accounts[index];
                                  final img =
                                      a.smallHeadImgUrl ?? a.bigHeadImgUrl;
                                  final String initial =
                                      (a.nickname ?? a.remark ?? a.wxId)
                                              .toString()
                                              .trim()
                                              .isNotEmpty
                                          ? (a.nickname ?? a.remark ?? a.wxId)
                                              .trim()
                                              .characters
                                              .first
                                              .toUpperCase()
                                          : '?';
                                  return InkWell(
                                    onTap: () => _onSelectAccount(a),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 6,
                                      ),
                                      child: Center(
                                        child: CircleAvatar(
                                          radius: 16,
                                          backgroundImage:
                                              img != null
                                                  ? NetworkImage(img)
                                                  : null,
                                          child:
                                              img == null
                                                  ? Text(initial)
                                                  : null,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                  ),
                  // 折叠/展开手柄
                  Positioned(
                    right: -8,
                    top: 12,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: () {
                          setState(() {
                            if (_sidebarWidth > 80) {
                              _sidebarWidth = 44; // 折叠到边上
                            } else {
                              _sidebarWidth = 280; // 展开默认宽度
                            }
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: const [
                              BoxShadow(blurRadius: 4, color: Colors.black12),
                            ],
                          ),
                          child: Icon(
                            _sidebarWidth > 80
                                ? Icons.chevron_left
                                : Icons.chevron_right,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const VerticalDivider(width: 1),
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child:
                      _loading
                          ? const Center(child: CircularProgressIndicator())
                          : TabBarView(
                            controller: _tabController,
                            children: _tabs.map((t) => t.page).toList(),
                          ),
                ),
                const Divider(height: 1),
                Material(
                  color: Theme.of(context).colorScheme.surface,
                  child: SafeArea(
                    top: false,
                    child: TabBar(
                      controller: _tabController,
                      tabs:
                          _tabs
                              .map(
                                (t) => Tab(text: t.label, icon: Icon(t.icon)),
                              )
                              .toList(),
                      labelPadding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _NavTab {
  final String label;
  final IconData icon;
  final Widget page;
  const _NavTab({required this.label, required this.icon, required this.page});
}
