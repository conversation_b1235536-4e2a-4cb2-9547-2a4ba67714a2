import 'dart:async';
import 'package:flutter/material.dart';
import '../models/gewe_message.dart';
import '../services/gewe_message_service.dart';
import '../services/gewe_service_manager.dart';
import '../widgets/message_bubble.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_widget.dart';

/// 聊天页面
class ChatPage extends StatefulWidget {
  final String contactWxId;
  final String contactName;
  final bool isGroup;

  const ChatPage({
    super.key,
    required this.contactWxId,
    required this.contactName,
    required this.isGroup,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final GeWeMessageService _messageService = GeWeMessageService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  List<GeWeMessage> _messages = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isSending = false;
  bool _hasMoreMessages = true;
  int _currentPage = 0;
  static const int _pageSize = 20;

  // 当前用户微信ID（实际应用中应该从用户状态管理中获取）
  String? _currentUserWxId;

  StreamSubscription<GeWeMessage>? _realtimeSub;

  @override
  void initState() {
    super.initState();
    _currentUserWxId = GeWeServiceManager.instance.currentUserWxId;
    _loadMessages();
    _setupScrollListener();

    // 如果是群聊，会话维度的 WS 需要带上 chatroomId
    if (widget.isGroup && _currentUserWxId != null) {
      // 先断开（如有），再以 chatroomId 维度连接
      GeWeServiceManager.instance.websocketService.disconnect().whenComplete(
        () {
          GeWeServiceManager.instance.websocketService.connect(
            currentUserWxId: _currentUserWxId!,
            chatroomId: widget.contactWxId,
          );
        },
      );
    }

    // 订阅实时消息
    _realtimeSub = GeWeServiceManager.instance.websocketService.messageStream
        .listen((msg) {
          // 只处理当前会话的消息
          final isCurrentChat =
              (msg.fromWxId == widget.contactWxId) ||
              (msg.toWxId == widget.contactWxId);
          if (!isCurrentChat) return;

          setState(() {
            _messages.add(msg);
          });

          // 当前会话内收到消息，清零未读
          GeWeServiceManager.instance.clearUnreadFor(widget.contactWxId);

          // 自动滚动到底部
          _scrollToBottom();
        });
  }

  @override
  void dispose() {
    _realtimeSub?.cancel();
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();

    // 离开群聊后，恢复为仅 wxId 的默认连接（以便接收私聊/全局消息）
    if (widget.isGroup && _currentUserWxId != null) {
      GeWeServiceManager.instance.websocketService.disconnect().whenComplete(
        () {
          GeWeServiceManager.instance.websocketService.connect(
            currentUserWxId: _currentUserWxId!,
          );
        },
      );
    }
    super.dispose();
  }

  /// 设置滚动监听器
  void _setupScrollListener() {
    _scrollController.addListener(() {
      // 当滚动到顶部时加载更多历史消息
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreMessages();
      }
    });
  }

  /// 加载消息
  Future<void> _loadMessages() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔍 Loading messages for contact: ${widget.contactWxId}');
      final response = await _messageService.getChatHistoryWithContact(
        currentUserWxId: _currentUserWxId!,
        contactWxId: widget.contactWxId,
        page: 0,
        size: _pageSize,
      );

      debugPrint(
        '📡 API Response: success=${response.isSuccess}, data=${response.data != null}',
      );
      if (response.data != null) {
        print('📊 Records count: ${response.data!.records?.length ?? 0}');
      }

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          final pageData = response.data!;
          final records = pageData.records ?? [];
          debugPrint('✅ Setting ${records.length} messages to state');

          // 添加详细的消息调试信息
          for (int i = 0; i < records.length; i++) {
            final msg = records[i];
            debugPrint(
              '📝 Message $i: id=${msg.id}, msgId=${msg.msgId}, msgType=${msg.msgType}, createTime=${msg.createTime}',
            );
          }

          setState(() {
            _messages =
                records
                    .where((msg) => msg.msgType == 1) // 只保留msgType为1的消息
                    .toList()
                    .reversed
                    .toList(); // 反转顺序，最新消息在底部
            _hasMoreMessages = pageData.hasNext;
            _currentPage = 0;
            _isLoading = false;
          });
          print('📱 Messages in state: ${_messages.length}');

          // 滚动到底部
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        } else {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('加载消息失败: ${response.message}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载消息失败: $e')));
      }
    }
  }

  /// 加载更多消息
  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || !_hasMoreMessages) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      debugPrint(
        '🔍 Loading more messages for contact 222: ${widget.contactWxId}',
      );
      final response = await _messageService.getChatHistoryWithContact(
        currentUserWxId: _currentUserWxId!,
        contactWxId: widget.contactWxId,
        page: _currentPage + 1,
        size: _pageSize,
      );

      debugPrint(
        '🔍 Loading more messages for contact: ${widget.contactWxId} - Page $response',
      );

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          final pageData = response.data!;
          final records = pageData.records ?? [];
          setState(() {
            // 在列表开头插入历史消息
            _messages.insertAll(0, records.reversed.toList());
            _hasMoreMessages = pageData.hasNext;
            _currentPage++;
            _isLoadingMore = false;
          });
        } else {
          setState(() {
            _isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    try {
      final response = await _messageService.sendMessageToContact(
        toWxId: widget.contactWxId,
        content: content,
      ); // fromWxid 由 service 自动填充

      if (mounted) {
        if (response.isSuccess) {
          // 清空输入框
          _messageController.clear();

          // 创建本地消息对象并添加到列表
          final localMessage = GeWeMessage(
            msgId: DateTime.now().millisecondsSinceEpoch.toString(),
            fromWxId: _currentUserWxId,
            toWxId: widget.contactWxId,
            content: content,
            msgType: MessageType.text.value,
            createTime: DateTime.now(),
            isGroup: widget.isGroup,
            isSent: true,
          );

          setState(() {
            _messages.add(localMessage);
          });

          // 滚动到底部
          _scrollToBottom();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('消息发送成功'),
              duration: Duration(seconds: 1),
            ),
          );
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('发送失败: ${response.message}')));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发送失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            if (widget.isGroup)
              Container(
                margin: const EdgeInsets.only(right: 8),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  '群',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            Expanded(
              child: Text(widget.contactName, overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: 显示更多选项
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('更多功能开发中...')));
            },
          ),
        ],
      ),
      body: Column(
        children: [Expanded(child: _buildMessageList()), _buildInputArea()],
      ),
    );
  }

  Widget _buildMessageList() {
    print(
      '🎨 Building message list: loading=$_isLoading, messages=${_messages.length}',
    );

    if (_isLoading && _messages.isEmpty) {
      print('⏳ Showing loading widget');
      return const LoadingWidget(message: '正在加载聊天记录...');
    }

    if (_messages.isEmpty) {
      print('📭 Showing empty widget');
      return const EmptyWidget(
        icon: Icons.chat_outlined,
        title: '暂无聊天记录',
        subtitle: '开始发送消息吧',
      );
    }

    print('📝 Building ListView with ${_messages.length} messages');

    return ListView.builder(
      controller: _scrollController,
      reverse: true, // 反向显示，最新消息在底部
      padding: const EdgeInsets.all(8),
      itemCount: _messages.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        // 显示加载更多指示器
        if (index == _messages.length) {
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final message = _messages[_messages.length - 1 - index]; // 反向索引
        final isFromCurrentUser =
            message.isSentMessage || (message.fromWxId == _currentUserWxId);

        return MessageBubble(
          message: message,
          isFromCurrentUser: isFromCurrentUser,
          showSenderName: widget.isGroup && !isFromCurrentUser,
          // 头像与昵称（从联系人信息里取，后续可由上层传入）
          avatarUrl: null, // 可接入联系人小头像 smallHeadImgUrl
          displayName: null, // 可接入联系人显示名 displayName/remark/nickName
        );
      },
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              focusNode: _messageFocusNode,
              decoration: InputDecoration(
                hintText: '输入消息...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              maxLines: 4,
              minLines: 1,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _isSending ? null : _sendMessage,
            icon:
                _isSending
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Icon(Icons.send),
            tooltip: '发送',
          ),
        ],
      ),
    );
  }
}
