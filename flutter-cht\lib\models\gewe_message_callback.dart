import 'package:json_annotation/json_annotation.dart';
import 'gewe_message.dart';

part 'gewe_message_callback.g.dart';

/// GeWe 消息回调请求模型
@JsonSerializable()
class GeWeMessageCallback {
  @Json<PERSON>ey(name: 'TypeName')
  final String? typeName;

  @J<PERSON><PERSON><PERSON>(name: 'Appid')
  final String? appid;

  @Json<PERSON>ey(name: 'Wxid')
  final String? wxid;

  @Json<PERSON>ey(name: 'Data')
  final MessageCallbackData? data;

  const GeWeMessageCallback({this.typeName, this.appid, this.wxid, this.data});

  /// 获取发送者微信ID
  String? get fromWxId => data?.fromUserName?.string;

  /// 获取接收者微信ID
  String? get toWxId => data?.toUserName?.string;

  /// 获取消息内容
  String? get content => data?.content?.string;

  /// 获取消息类型
  int? get msgType => data?.msgType;

  /// 获取消息ID
  String? get msgId => data?.msgId;

  /// 获取创建时间
  DateTime? get createTime => data?.createTime;

  factory GeWeMessageCallback.fromJson(Map<String, dynamic> json) =>
      _$GeWeMessageCallbackFromJson(json);

  Map<String, dynamic> toJson() => _$GeWeMessageCallbackToJson(this);

  @override
  String toString() {
    return 'GeWeMessageCallback{typeName: $typeName, from: $fromWxId, to: $toWxId, msgType: $msgType, content: $content}';
  }
}

/// 消息回调数据
@JsonSerializable()
class MessageCallbackData {
  @JsonKey(name: 'MsgId')
  final String? msgId;

  @JsonKey(name: 'FromUserName')
  final StringWrapper? fromUserName;

  @JsonKey(name: 'ToUserName')
  final StringWrapper? toUserName;

  @JsonKey(name: 'MsgType')
  final int? msgType;

  @JsonKey(name: 'Content')
  final StringWrapper? content;

  @JsonKey(name: 'Status')
  final int? status;

  @JsonKey(name: 'ImgStatus')
  final int? imgStatus;

  @JsonKey(name: 'ImgBuf')
  final ImgBuf? imgBuf;

  @JsonKey(name: 'CreateTime')
  final DateTime? createTime;

  @JsonKey(name: 'MsgSource')
  final String? msgSource;

  @JsonKey(name: 'PushContent')
  final String? pushContent;

  @JsonKey(name: 'NewMsgId')
  final int? newMsgId;

  @JsonKey(name: 'MsgSeq')
  final int? msgSeq;

  const MessageCallbackData({
    this.msgId,
    this.fromUserName,
    this.toUserName,
    this.msgType,
    this.content,
    this.status,
    this.imgStatus,
    this.imgBuf,
    this.createTime,
    this.msgSource,
    this.pushContent,
    this.newMsgId,
    this.msgSeq,
  });

  factory MessageCallbackData.fromJson(Map<String, dynamic> json) =>
      _$MessageCallbackDataFromJson(json);

  Map<String, dynamic> toJson() => _$MessageCallbackDataToJson(this);
}

/// 字符串包装类
@JsonSerializable()
class StringWrapper {
  @JsonKey(name: 'string')
  final String? string;

  const StringWrapper({this.string});

  factory StringWrapper.fromJson(Map<String, dynamic> json) =>
      _$StringWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$StringWrapperToJson(this);
}

/// 图片缓冲区类
@JsonSerializable()
class ImgBuf {
  @JsonKey(name: 'iLen')
  final int? iLen;

  const ImgBuf({this.iLen});

  factory ImgBuf.fromJson(Map<String, dynamic> json) => _$ImgBufFromJson(json);

  Map<String, dynamic> toJson() => _$ImgBufToJson(this);
}

/// 消息回调事件类型
enum CallbackEventType {
  addMsg('AddMsg', '新消息'),
  delMsg('DelMsg', '删除消息'),
  modifyMsg('ModifyMsg', '修改消息'),
  unknown('Unknown', '未知事件');

  const CallbackEventType(this.value, this.description);

  final String value;
  final String description;

  static CallbackEventType fromValue(String? value) {
    return CallbackEventType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => CallbackEventType.unknown,
    );
  }
}

/// 消息回调处理结果
class MessageCallbackResult {
  final bool success;
  final String message;
  final GeWeMessage? parsedMessage;
  final CallbackEventType eventType;

  const MessageCallbackResult({
    required this.success,
    required this.message,
    this.parsedMessage,
    required this.eventType,
  });

  factory MessageCallbackResult.success({
    required GeWeMessage message,
    required CallbackEventType eventType,
  }) {
    return MessageCallbackResult(
      success: true,
      message: '消息处理成功',
      parsedMessage: message,
      eventType: eventType,
    );
  }

  factory MessageCallbackResult.failure({
    required String message,
    CallbackEventType eventType = CallbackEventType.unknown,
  }) {
    return MessageCallbackResult(
      success: false,
      message: message,
      eventType: eventType,
    );
  }
}
