import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../services/message_notification_service.dart';

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  static bool _isRedirectingToLogin = false;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    String errorMessage = _getErrorMessage(err);

    if (kDebugMode) {
      print('❌ Error Interceptor - ${err.type}: $errorMessage');
      print(
        '❌ Request: ${err.requestOptions.method} ${err.requestOptions.uri}',
      );
      if (err.response != null) {
        print('❌ Response: ${err.response?.statusCode} ${err.response?.data}');
      }
    }

    // 403 时跳转登录（避免对登录请求本身触发重定向）
    try {
      final status = err.response?.statusCode;
      final path = err.requestOptions.path;
      if (status == 403 && (path.contains('login') == false)) {
        if (kDebugMode) {
          print('🔐 HTTP 403 detected, navigating to /login');
        }
        final navKey = MessageNotificationService.navigatorKey;
        if (!_isRedirectingToLogin && navKey?.currentState != null) {
          _isRedirectingToLogin = true;
          // 用微任务避免在拦截器同步栈中直接导航
          Future.microtask(() {
            // 清空栈并进入登录页，防止返回到受限页
            navKey!.currentState!.pushNamedAndRemoveUntil(
              '/login',
              (route) => false,
            );
            _isRedirectingToLogin = false;
          });
        }
      }
    } catch (_) {
      // 忽略导航异常，继续抛出原始错误
    }

    // 创建自定义的 DioException 以提供更友好的错误信息
    final customError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: err.error,
      message: errorMessage,
    );

    super.onError(customError, handler);
  }

  /// 获取友好的错误信息
  String _getErrorMessage(DioException err) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络连接';
      case DioExceptionType.sendTimeout:
        return '发送超时，请检查网络连接';
      case DioExceptionType.receiveTimeout:
        return '接收超时，请检查网络连接';
      case DioExceptionType.badCertificate:
        return '证书验证失败';
      case DioExceptionType.badResponse:
        return _getHttpErrorMessage(err.response?.statusCode);
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接失败，请检查网络设置';
      case DioExceptionType.unknown:
        if (err.error.toString().contains('SocketException')) {
          return '网络连接失败，请检查网络设置';
        }
        return '未知错误：${err.error}';
    }
  }

  /// 获取 HTTP 状态码对应的错误信息
  String _getHttpErrorMessage(int? statusCode) {
    switch (statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '禁止访问';
      case 404:
        return '请求的资源不存在';
      case 405:
        return '请求方法不允许';
      case 408:
        return '请求超时';
      case 409:
        return '请求冲突';
      case 422:
        return '请求参数验证失败';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂时不可用';
      case 504:
        return '网关超时';
      default:
        return '服务器错误 ($statusCode)';
    }
  }
}
