import 'package:flutter/material.dart';
import '../pages/recent_contacts_page.dart';
import '../models/gewe_message.dart';

/// 最近联系人列表项组件
class RecentContactItem extends StatelessWidget {
  final RecentContact recentContact;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const RecentContactItem({
    super.key,
    required this.recentContact,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(recentContact.contact.wxId),
      direction: DismissDirection.endToStart,
      background: _buildDismissBackground(),
      confirmDismiss: (direction) async {
        return await _showDeleteConfirmDialog(context);
      },
      onDismissed: (direction) {
        onDelete?.call();
      },
      child: ListTile(
        leading: _buildAvatar(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        trailing: _buildTrailing(),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _buildAvatar() {
    return Stack(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundImage:
              recentContact.contact.avatarUrl != null
                  ? NetworkImage(recentContact.contact.avatarUrl!)
                  : null,
          backgroundColor:
              recentContact.contact.isGroupChat
                  ? Colors.green[300]
                  : Colors.grey[300],
          child:
              recentContact.contact.avatarUrl == null
                  ? Icon(
                    recentContact.contact.isGroupChat
                        ? Icons.group
                        : Icons.person,
                    color: Colors.white,
                    size: 24,
                  )
                  : null,
        ),
        if (recentContact.unreadCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
              child: Text(
                recentContact.unreadCount > 99
                    ? '99+'
                    : recentContact.unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTitle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            recentContact.contact.name,
            style: TextStyle(
              fontSize: 16,
              fontWeight:
                  recentContact.unreadCount > 0
                      ? FontWeight.bold
                      : FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (recentContact.contact.isGroupChat)
          Container(
            margin: const EdgeInsets.only(left: 8),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.green[100],
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Text(
              '群',
              style: TextStyle(
                fontSize: 10,
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSubtitle() {
    String lastMessageText = '';

    if (recentContact.lastMessage != null) {
      final message = recentContact.lastMessage!;
      switch (message.messageType) {
        case MessageType.text:
          lastMessageText = message.content ?? '';
          break;
        case MessageType.image:
          lastMessageText = '[图片]';
          break;
        case MessageType.voice:
          lastMessageText = '[语音]';
          break;
        case MessageType.video:
          lastMessageText = '[视频]';
          break;
        case MessageType.file:
          lastMessageText = '[文件]';
          break;
        case MessageType.location:
          lastMessageText = '[位置]';
          break;
        case MessageType.emoji:
          lastMessageText = '[表情]';
          break;
        case MessageType.system:
          lastMessageText = message.content ?? '[系统消息]';
          break;
      }
    } else {
      lastMessageText = '暂无消息';
    }

    return Text(
      lastMessageText,
      style: TextStyle(
        fontSize: 14,
        color:
            recentContact.unreadCount > 0 ? Colors.black87 : Colors.grey[600],
        fontWeight:
            recentContact.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTrailing() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          _formatTime(recentContact.lastMessageTime),
          style: TextStyle(
            fontSize: 12,
            color:
                recentContact.unreadCount > 0 ? Colors.red : Colors.grey[500],
            fontWeight:
                recentContact.unreadCount > 0
                    ? FontWeight.bold
                    : FontWeight.normal,
          ),
        ),
        if (recentContact.unreadCount > 0) const SizedBox(height: 4),
      ],
    );
  }

  Widget _buildDismissBackground() {
    return Container(
      color: Colors.red,
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 20),
      child: const Icon(Icons.delete, color: Colors.white, size: 24),
    );
  }

  Future<bool?> _showDeleteConfirmDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('删除联系人'),
            content: Text('确定要从最近联系人中删除 ${recentContact.contact.name} 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return '昨天';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}天前';
      } else {
        return '${time.month}/${time.day}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
