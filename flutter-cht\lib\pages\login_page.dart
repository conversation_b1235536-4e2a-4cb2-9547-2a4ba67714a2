import 'package:flutter/material.dart';
import '../services/login_service.dart';
import '../storage/token_storage.dart';
import '../network/dio_client.dart';
import '../services/gewe_service_manager.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _tenantController = TextEditingController(text: '100');
  bool _loading = false;
  final _loginService = LoginService();

  Future<void> _doLogin() async {
    setState(() => _loading = true);
    try {
      final resp = await _loginService.login(
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        tenantId: int.tryParse(_tenantController.text) ?? 100,
      );
      if (resp.isSuccess && resp.data != null) {
        final token = resp.data!.token;
        await TokenStorage.saveToken(token);
        DioClient.instance.setAuthToken(token);
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('登录成功')));
        }
        // 进入主页面（替换当前登录页，避免返回白屏）
        if (!mounted) return;
        Navigator.of(context).pushReplacementNamed('/main');
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('登录失败：${resp.message}')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('登录异常：$e')));
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('登录')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(labelText: '手机号'),
              keyboardType: TextInputType.phone,
            ),
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(labelText: '密码'),
              obscureText: true,
            ),
            TextField(
              controller: _tenantController,
              decoration: const InputDecoration(labelText: '租户ID'),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loading ? null : _doLogin,
              child:
                  _loading
                      ? const CircularProgressIndicator()
                      : const Text('登录'),
            ),
          ],
        ),
      ),
    );
  }
}
