{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-85.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-7.7.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_resolvers-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner_core-9.1.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "built_collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.11.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_style-3.1.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dbus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "dio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio-5.9.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///E:/software/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_local_notifications", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_local_notifications-17.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_local_notifications_linux-4.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_local_notifications_platform_interface-7.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///E:/software/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///E:/software/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "frontend_server_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "graphs", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-1.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_multi_server", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_serializable-6.9.5", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "logger", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logger-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.12", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sky_engine", "rootUri": "file:///E:/software/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_gen", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_helper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_helper-1.3.7", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timezone", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timezone-0.9.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "timing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.3", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "im_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-09-01T06:23:55.284394Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///E:/software/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}