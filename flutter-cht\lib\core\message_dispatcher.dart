import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gewe_message.dart';

/// 消息事件类型
enum MessageEventType {
  newMessage,
  messageRead,
  messageDeleted,
  friendOnline,
  friendOffline,
  groupMemberJoined,
  groupMemberLeft,
}

/// 消息事件
class MessageEvent {
  final MessageEventType type;
  final GeWeMessage? message;
  final String? wxId;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  MessageEvent({
    required this.type,
    this.message,
    this.wxId,
    this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'MessageEvent{type: $type, wxId: $wxId, timestamp: $timestamp}';
  }
}

/// 消息监听器
typedef MessageListener = void Function(MessageEvent event);

/// 消息分发器 - 负责消息的路由和事件分发
class MessageDispatcher {
  static MessageDispatcher? _instance;

  // 事件流控制器
  final Map<MessageEventType, StreamController<MessageEvent>>
  _eventControllers = {};
  final StreamController<MessageEvent> _allEventsController =
      StreamController.broadcast();

  // 监听器
  final Map<String, MessageListener> _listeners = {};
  final Map<String, List<MessageEventType>> _listenerTypes = {};

  // 消息缓存
  final Map<String, List<GeWeMessage>> _messageCache = {};
  final Map<String, int> _unreadCounts = {};

  // 配置
  int _maxCacheSize = 100;
  bool _enableCache = true;

  MessageDispatcher._internal() {
    _initializeEventControllers();
  }

  static MessageDispatcher get instance {
    _instance ??= MessageDispatcher._internal();
    return _instance!;
  }

  /// 所有事件流
  Stream<MessageEvent> get allEventsStream => _allEventsController.stream;

  /// 未读消息计数
  Map<String, int> get unreadCounts => Map.unmodifiable(_unreadCounts);

  /// 消息缓存
  Map<String, List<GeWeMessage>> get messageCache =>
      Map.unmodifiable(_messageCache);

  /// 初始化事件控制器
  void _initializeEventControllers() {
    for (final eventType in MessageEventType.values) {
      _eventControllers[eventType] = StreamController.broadcast();
    }
  }

  /// 获取特定类型的事件流
  Stream<MessageEvent> getEventStream(MessageEventType type) {
    return _eventControllers[type]?.stream ?? const Stream.empty();
  }

  /// 分发新消息事件
  void dispatchNewMessage(GeWeMessage message) {
    final event = MessageEvent(
      type: MessageEventType.newMessage,
      message: message,
      wxId: message.fromWxId,
    );

    _dispatchEvent(event);

    // 更新缓存和未读计数
    if (_enableCache) {
      _updateMessageCache(message);
      _updateUnreadCount(message);
    }

    if (kDebugMode) {
      print('📨 Dispatched new message: ${message.toString()}');
    }
  }

  /// 分发消息已读事件
  void dispatchMessageRead(String contactWxId, {List<int>? messageIds}) {
    final event = MessageEvent(
      type: MessageEventType.messageRead,
      wxId: contactWxId,
      data: {'messageIds': messageIds},
    );

    _dispatchEvent(event);

    // 清除未读计数
    _unreadCounts[contactWxId] = 0;

    if (kDebugMode) {
      print('👁️ Dispatched message read: $contactWxId');
    }
  }

  /// 分发消息删除事件
  void dispatchMessageDeleted(String contactWxId, List<int> messageIds) {
    final event = MessageEvent(
      type: MessageEventType.messageDeleted,
      wxId: contactWxId,
      data: {'messageIds': messageIds},
    );

    _dispatchEvent(event);

    // 从缓存中移除消息
    if (_enableCache && _messageCache.containsKey(contactWxId)) {
      _messageCache[contactWxId]?.removeWhere(
        (msg) => messageIds.contains(msg.msgId),
      );
    }

    if (kDebugMode) {
      print('🗑️ Dispatched message deleted: $contactWxId, ids: $messageIds');
    }
  }

  /// 分发好友在线事件
  void dispatchFriendOnline(String wxId) {
    final event = MessageEvent(type: MessageEventType.friendOnline, wxId: wxId);

    _dispatchEvent(event);

    if (kDebugMode) {
      print('🟢 Dispatched friend online: $wxId');
    }
  }

  /// 分发好友离线事件
  void dispatchFriendOffline(String wxId) {
    final event = MessageEvent(
      type: MessageEventType.friendOffline,
      wxId: wxId,
    );

    _dispatchEvent(event);

    if (kDebugMode) {
      print('🔴 Dispatched friend offline: $wxId');
    }
  }

  /// 分发群成员加入事件
  void dispatchGroupMemberJoined(String groupWxId, String memberWxId) {
    final event = MessageEvent(
      type: MessageEventType.groupMemberJoined,
      wxId: groupWxId,
      data: {'memberWxId': memberWxId},
    );

    _dispatchEvent(event);

    if (kDebugMode) {
      print(
        '➕ Dispatched group member joined: $groupWxId, member: $memberWxId',
      );
    }
  }

  /// 分发群成员离开事件
  void dispatchGroupMemberLeft(String groupWxId, String memberWxId) {
    final event = MessageEvent(
      type: MessageEventType.groupMemberLeft,
      wxId: groupWxId,
      data: {'memberWxId': memberWxId},
    );

    _dispatchEvent(event);

    if (kDebugMode) {
      print('➖ Dispatched group member left: $groupWxId, member: $memberWxId');
    }
  }

  /// 内部事件分发方法
  void _dispatchEvent(MessageEvent event) {
    // 分发到特定类型的流
    _eventControllers[event.type]?.add(event);

    // 分发到全局事件流
    _allEventsController.add(event);

    // 调用注册的监听器
    _notifyListeners(event);
  }

  /// 通知监听器
  void _notifyListeners(MessageEvent event) {
    for (final entry in _listenerTypes.entries) {
      final listenerId = entry.key;
      final types = entry.value;

      if (types.contains(event.type)) {
        final listener = _listeners[listenerId];
        if (listener != null) {
          try {
            listener(event);
          } catch (e) {
            if (kDebugMode) {
              print('❌ Error in listener $listenerId: $e');
            }
          }
        }
      }
    }
  }

  /// 注册消息监听器
  String addListener(
    MessageListener listener, {
    List<MessageEventType>? eventTypes,
  }) {
    final listenerId = DateTime.now().millisecondsSinceEpoch.toString();
    _listeners[listenerId] = listener;
    _listenerTypes[listenerId] = eventTypes ?? MessageEventType.values;

    if (kDebugMode) {
      print('📡 Added listener: $listenerId for types: ${eventTypes ?? 'all'}');
    }

    return listenerId;
  }

  /// 移除监听器
  void removeListener(String listenerId) {
    _listeners.remove(listenerId);
    _listenerTypes.remove(listenerId);

    if (kDebugMode) {
      print('📡 Removed listener: $listenerId');
    }
  }

  /// 更新消息缓存
  void _updateMessageCache(GeWeMessage message) {
    final contactWxId = _getContactWxId(message);

    if (!_messageCache.containsKey(contactWxId)) {
      _messageCache[contactWxId] = [];
    }

    final messages = _messageCache[contactWxId]!;
    messages.add(message);

    // 限制缓存大小
    if (messages.length > _maxCacheSize) {
      messages.removeRange(0, messages.length - _maxCacheSize);
    }
  }

  /// 更新未读计数
  void _updateUnreadCount(GeWeMessage message) {
    final contactWxId = _getContactWxId(message);
    _unreadCounts[contactWxId] = (_unreadCounts[contactWxId] ?? 0) + 1;
  }

  /// 获取联系人微信ID（用于缓存键）
  String _getContactWxId(GeWeMessage message) {
    // 如果是群聊消息，使用群聊ID
    if (message.isGroupMessage) {
      return message.toWxId ?? message.fromWxId ?? 'unknown';
    }
    // 如果是私聊消息，使用对方的ID
    return message.fromWxId ?? message.toWxId ?? 'unknown';
  }

  /// 获取联系人的消息缓存
  List<GeWeMessage> getContactMessages(String contactWxId) {
    return _messageCache[contactWxId] ?? [];
  }

  /// 获取联系人的未读计数
  int getUnreadCount(String contactWxId) {
    return _unreadCounts[contactWxId] ?? 0;
  }

  /// 清除联系人的未读计数
  void clearUnreadCount(String contactWxId) {
    _unreadCounts[contactWxId] = 0;
    dispatchMessageRead(contactWxId);
  }

  /// 配置缓存设置
  void configureCaching({bool? enabled, int? maxCacheSize}) {
    if (enabled != null) _enableCache = enabled;
    if (maxCacheSize != null) _maxCacheSize = maxCacheSize;
  }

  /// 清除所有缓存
  void clearCache() {
    _messageCache.clear();
    _unreadCounts.clear();
  }

  /// 释放资源
  void dispose() {
    for (final controller in _eventControllers.values) {
      controller.close();
    }
    _allEventsController.close();
    _listeners.clear();
    _listenerTypes.clear();
    clearCache();
  }
}
