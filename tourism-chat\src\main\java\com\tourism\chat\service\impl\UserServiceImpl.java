package com.tourism.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tourism.chat.entity.User;
import com.tourism.chat.mapper.UserMapper;
import com.tourism.chat.service.UserService;
import org.springframework.stereotype.Service;

/**
 * Implementation of UserService using MyBatis-Plus.
 */
@Service
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    public UserServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public User findByNameAndTenantId(String phone, Long tenantId) {
        return userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getPhone, phone)
                .eq(User::getTenantId, tenantId)
                .last("limit 1"));
    }
}

