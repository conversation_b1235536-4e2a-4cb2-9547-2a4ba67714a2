// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gewe_friend.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeWeFriend _$GeWeFriendFromJson(Map<String, dynamic> json) => GeWeFriend(
  id: json['id'] as String?,
  wxId: json['wxId'] as String,
  nickName: json['nickName'] as String?,
  pyInitial: json['pyInitial'] as String?,
  quanPin: json['quanPin'] as String?,
  sex: (json['sex'] as num?)?.toInt(),
  remark: json['remark'] as String?,
  remarkPyInitial: json['remarkPyInitial'] as String?,
  remarkQuanPin: json['remarkQuanPin'] as String?,
  signature: json['signature'] as String?,
  alias: json['alias'] as String?,
  snsBgImg: json['snsBgImg'] as String?,
  country: json['country'] as String?,
  province: json['province'] as String?,
  city: json['city'] as String?,
  bigHeadImgUrl: json['bigHeadImgUrl'] as String?,
  smallHeadImgUrl: json['smallHeadImgUrl'] as String?,
  isGroup: json['isGroup'] as bool?,
  displayName: json['displayName'] as String?,
);

Map<String, dynamic> _$GeWeFriendToJson(GeWeFriend instance) =>
    <String, dynamic>{
      'id': instance.id,
      'wxId': instance.wxId,
      'nickName': instance.nickName,
      'pyInitial': instance.pyInitial,
      'quanPin': instance.quanPin,
      'sex': instance.sex,
      'remark': instance.remark,
      'remarkPyInitial': instance.remarkPyInitial,
      'remarkQuanPin': instance.remarkQuanPin,
      'signature': instance.signature,
      'alias': instance.alias,
      'snsBgImg': instance.snsBgImg,
      'country': instance.country,
      'province': instance.province,
      'city': instance.city,
      'bigHeadImgUrl': instance.bigHeadImgUrl,
      'smallHeadImgUrl': instance.smallHeadImgUrl,
      'isGroup': instance.isGroup,
      'displayName': instance.displayName,
    };

SyncFriendsRequest _$SyncFriendsRequestFromJson(Map<String, dynamic> json) =>
    SyncFriendsRequest(
      wxIds:
          (json['wxIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$SyncFriendsRequestToJson(SyncFriendsRequest instance) =>
    <String, dynamic>{'wxIds': instance.wxIds};

SyncFriendsResponse _$SyncFriendsResponseFromJson(Map<String, dynamic> json) =>
    SyncFriendsResponse(
      success: json['success'] as bool,
      syncCount: (json['syncCount'] as num?)?.toInt(),
      message: json['message'] as String,
    );

Map<String, dynamic> _$SyncFriendsResponseToJson(
  SyncFriendsResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  'syncCount': instance.syncCount,
  'message': instance.message,
};
