package com.tourism.chat.tenant;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.tourism.chat.entity.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @PROJECT dcp-base
 * @Description automatically fill in these fields when persisting into database
 * @Date 2020年10月30日
 */
@Slf4j
public class DefaultMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
            log.info("start insert fill for time columns...");
        Long tenantId = TenantContextHolder.getTenantId();
        UserContext context = TenantContextHolder.getContext();
        if (!context.getIgnoreTenant()) {
            this.setFieldValByName(BaseEntity.TENANT_ID, tenantId, metaObject);
        }
        this.setFieldValByName(BaseEntity.CREATED_BY, context.getUserId(), metaObject);
        this.setFieldValByName(BaseEntity.CREATED_TIME, LocalDateTime.now(), metaObject);
        this.setFieldValByName(BaseEntity.LAST_UPDATED_TIME, LocalDateTime.now(), metaObject);
        // UserContext userContext = UserContextHolder.getContext();
        // if (ObjectUtil.isNotNull(userContext.getUserId()) && BooleanUtil.isTrue(userContext.isFillUser())) {
        //     String userId = String.valueOf(userContext.getUserId());
        //     this.setFieldValByName(BaseEntity.CREATED_BY, userId, metaObject);
        //     this.setFieldValByName(BaseEntity.MODIFIED_BY, userId, metaObject);
        // }
        // if (ObjectUtil.isNotNull(userContext.getTenantId())) {
        //     Long tenantId = userContext.getTenantId();
        //     if (log.isDebugEnabled()) {
        //         log.debug("ignore tenant status {}, tenantId is {}", userContext.getIgnoreTenant(), tenantId);
        //     }
        //     if (!userContext.getIgnoreTenant()) {
        //         this.setFieldValByName(BaseEntity.TENANT_ID, tenantId, metaObject);
        //     }
        // }
        //
        // if (BooleanUtil.isTrue(userContext.isFillTime())) {
        //     this.setFieldValByName(BaseEntity.CREATED_TIME, new Date(), metaObject);
        // }
        // this.setFieldValByName(BaseEntity.IS_DELETED, false, metaObject);
        // if (BooleanUtil.isTrue(userContext.isFillModifiedTime())) {
        //     this.setFieldValByName(BaseEntity.LAST_UPDATED_TIME, new Date(), metaObject);
        // }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (log.isDebugEnabled()) {
            log.debug("start updating fill for time columns...");
        }
        // if (ObjectUtil.isNotNull(UserContextHolder.getContext().getUserId())) {
        //     String userId = String.valueOf(UserContextHolder.getContext().getUserId());
        //     this.setFieldValByName(BaseEntity.MODIFIED_BY, userId, metaObject);
        // }
        this.setFieldValByName(BaseEntity.LAST_UPDATED_TIME, LocalDateTime.now(), metaObject);
        UserContext context = TenantContextHolder.getContext();
        this.setFieldValByName(BaseEntity.MODIFIED_BY, context.getUserId(), metaObject);
    }
}
