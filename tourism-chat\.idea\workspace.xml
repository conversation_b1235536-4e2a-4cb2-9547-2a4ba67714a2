<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2f9b4bef-5d97-45e0-afc8-a8aa6b06a3cc" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/README_Redis_Message_System.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README_SocketIO_JWT_Integration.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/RedisMessageSubscriber使用说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/SocketIO_JWT认证使用说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/常量整理说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/constants/RedisKeyConstants.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/ChatMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/SocketIoServerRunner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/auth/SocketJwtAuthInterceptor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/config/SocketIoConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/redis/GroupMessageSubscriber.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/redis/MessagePublisher.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/redis/PrivateMessageSubscriber.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/socket/redis/SystemMessageSubscriber.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/tourism/chat/socket/MessageSubscriberTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/tourism/chat/socket/SocketJwtAuthTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/config/RedisConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/config/RedisConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="325vaRoBMeonVzZlyDnm40LKpGD" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP Request.generated-requests | #140.executor&quot;: &quot;Run&quot;,
    &quot;Maven.tourism-chat [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.DemoApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/me/tourism-chat/pom.xml&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.DemoApplication">
    <configuration name="generated-requests | #140" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#140" index="140" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="DemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tourism-chat" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tourism.chat.DemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.generated-requests | #140" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2f9b4bef-5d97-45e0-afc8-a8aa6b06a3cc" name="Changes" comment="" />
      <created>1756723310604</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756723310604</updated>
      <workItem from="1756723311723" duration="4954000" />
      <workItem from="1756778404492" duration="9884000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>