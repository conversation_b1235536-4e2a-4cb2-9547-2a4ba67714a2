package com.tourism.chat.wechat.controller;

import com.tourism.chat.common.response.R;
import com.tourism.chat.tenant.TenantContextHolder;
import com.tourism.chat.wechat.dto.WechatAccountDTO;
import com.tourism.chat.wechat.service.GeweWechatAcountService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import com.tourism.chat.auth.JwtTokenProvider;

@RestController
@RequestMapping("/wechat")
@RequiredArgsConstructor
public class WechatAccountController {

    private final GeweWechatAcountService service;
    private final JwtTokenProvider tokenProvider;

    @GetMapping("/accounts")
    public R<List<WechatAccountDTO>> getAccounts(HttpServletRequest request) {
        try {
            Long userId = TenantContextHolder.getContext().getUserId();
            List<WechatAccountDTO> list = service.findAccountsByUser(userId);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail("查询失败:" + e.getMessage());
        }
    }
}

