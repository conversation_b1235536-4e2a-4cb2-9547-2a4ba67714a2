import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gewe_message.dart';
import '../models/gewe_message_callback.dart';
import '../network/websocket_client.dart';
import 'gewe_friend_service.dart';
import 'gewe_message_service.dart';
import 'gewe_websocket_service.dart';
import 'message_notification_service.dart';

/// GeWe 服务管理器 - 统一管理所有 GeWe 相关服务
class GeWeServiceManager {
  static GeWeServiceManager? _instance;

  // 服务实例
  late final GeWeFriendService _friendService;
  late final GeWeMessageService _messageService;
  late final GeWeWebSocketService _websocketService;
  late final MessageNotificationService _notificationService;

  // 流订阅
  StreamSubscription? _messageSubscription;
  StreamSubscription? _callbackSubscription;
  StreamSubscription? _connectionSubscription;

  // 状态
  bool _isInitialized = false;
  String? _currentUserWxId;

  // 账号选择变更广播
  final StreamController<String?> _accountController =
      StreamController.broadcast();

  // 未读计数：contactWxId -> count
  final Map<String, int> _unreadMap = {};
  final StreamController<Map<String, int>> _unreadController =
      StreamController.broadcast();

  GeWeServiceManager._internal() {
    _friendService = GeWeFriendService();
    _messageService = GeWeMessageService();
    _websocketService = GeWeWebSocketService.instance;
    _notificationService = MessageNotificationService.instance;
  }

  static GeWeServiceManager get instance {
    _instance ??= GeWeServiceManager._internal();
    return _instance!;
  }

  // 服务访问器
  GeWeFriendService get friendService => _friendService;
  GeWeMessageService get messageService => _messageService;
  GeWeWebSocketService get websocketService => _websocketService;
  MessageNotificationService get notificationService => _notificationService;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 当前用户微信ID
  String? get currentUserWxId => _currentUserWxId;

  /// WebSocket 连接状态
  bool get isConnected => _websocketService.isConnected;

  /// 初始化服务管理器
  Future<void> initialize({String? currentUserWxId}) async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing GeWe Service Manager...');
      }

      _currentUserWxId = currentUserWxId;

      // 初始化通知服务
      await _notificationService.initialize();

      // 设置消息监听
      _setupMessageListeners();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ GeWe Service Manager initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize GeWe Service Manager: $e');
      }
      rethrow;
    }
  }

  /// 设置消息监听器
  void _setupMessageListeners() {
    // 监听新消息
    _messageSubscription = _websocketService.messageStream.listen(
      _handleNewMessage,
      onError: (error) {
        if (kDebugMode) {
          print('❌ Message stream error: $error');
        }
      },
    );

    // 监听回调处理结果
    _callbackSubscription = _websocketService.callbackResultStream.listen(
      _handleCallbackResult,
      onError: (error) {
        if (kDebugMode) {
          print('❌ Callback stream error: $error');
        }
      },
    );

    // 监听连接状态
    _connectionSubscription = _websocketService.connectionStatusStream.listen(
      _handleConnectionStatusChange,
      onError: (error) {
        if (kDebugMode) {
          print('❌ Connection status stream error: $error');
        }
      },
    );
  }

  /// 账号变更流
  Stream<String?> get accountStream => _accountController.stream;

  /// 未读计数流
  Stream<Map<String, int>> get unreadStream => _unreadController.stream;
  Map<String, int> get unreadSnapshot => Map.unmodifiable(_unreadMap);

  /// 处理新消息
  void _handleNewMessage(GeWeMessage message) {
    if (kDebugMode) {
      print('📨 New message received: ${message.toString()}');
    }

    // 显示通知（如果不是自己发送的消息）
    if (message.fromWxId != _currentUserWxId) {
      _notificationService.showMessageNotification(message);
    }

    // 更新未读：仅统计接收的消息
    final from = message.fromWxId;
    final to = message.toWxId;
    final isRecv = message.isSentMessage == false;
    if (isRecv && from != null) {
      // 如果当前正在查看该会话，可以由页面清零；这里先+1
      _unreadMap[from] = (_unreadMap[from] ?? 0) + 1;
      _unreadController.add(Map.unmodifiable(_unreadMap));
    }
  }

  /// 清零某会话未读，并通知
  void clearUnreadFor(String contactWxId) {
    if (_unreadMap.containsKey(contactWxId)) {
      _unreadMap.remove(contactWxId);
      _unreadController.add(Map.unmodifiable(_unreadMap));
    }
  }

  /// 处理回调结果
  void _handleCallbackResult(MessageCallbackResult result) {
    if (kDebugMode) {
      print(
        '📋 Callback result: ${result.success ? 'Success' : 'Failed'} - ${result.message}',
      );
    }

    if (!result.success) {
      // 处理回调失败的情况
      if (kDebugMode) {
        print('❌ Message callback failed: ${result.message}');
      }
    }
  }

  /// 处理连接状态变化
  void _handleConnectionStatusChange(WebSocketStatus status) {
    if (kDebugMode) {
      print('🔌 Connection status changed: $status');
    }

    // TODO: 可以在这里添加连接状态变化的处理逻辑
    // 如显示连接状态提示、重连逻辑等
  }

  /// 连接 WebSocket
  Future<void> connect({String? token}) async {
    if (_currentUserWxId == null || _currentUserWxId!.isEmpty) {
      if (kDebugMode) {
        print('❌ connect: currentUserWxId 未设置');
      }
      return;
    }
    await _websocketService.connect(
      token: token ?? _getAuthTokenSafely(),
      currentUserWxId: _currentUserWxId!,
    );
  }

  // 从 DioClient 中尽量取出当前 Authorization Bearer token，若无则返回 null
  String? _getAuthTokenSafely() {
    try {
      // 由于 DioClient 暴露了 setAuthToken，但未暴露读取方法，这里可以考虑维护一份副本；
      // 先尝试通过全局拦截器拿（如果你有全局存储 token 的地方，可以替换这段）。
      return null; // 占位：如果你有 AppPrefs 或 AuthService，可在此读取
    } catch (_) {
      return null;
    }
  }

  /// 断开 WebSocket 连接
  Future<void> disconnect() async {
    await _websocketService.disconnect();
  }

  /// 设置当前用户微信ID
  void setCurrentUserWxId(String wxId) {
    _currentUserWxId = wxId;
    _accountController.add(_currentUserWxId);
  }

  /// 快捷方法：同步好友信息
  Future<void> syncFriends({List<String>? wxIds}) async {
    final response = await _friendService.syncFriends(wxIds: wxIds);
    if (response.isSuccess) {
      if (kDebugMode) {
        print('✅ Friends synced: ${response.data?.message}');
      }
    } else {
      if (kDebugMode) {
        print('❌ Failed to sync friends: ${response.message}');
      }
    }
  }

  /// 快捷方法：发送消息
  Future<bool> sendMessage({
    required String toWxId,
    required String content,
    List<String>? atWxIds,
  }) async {
    final response = await _messageService.sendMessageToContact(
      toWxId: toWxId,
      content: content,
      atWxIds: atWxIds,
    );

    if (response.isSuccess) {
      if (kDebugMode) {
        print('✅ Message sent successfully');
      }
      return true;
    } else {
      if (kDebugMode) {
        print('❌ Failed to send message: ${response.message}');
      }
      return false;
    }
  }

  /// 快捷方法：获取聊天历史
  Future<List<GeWeMessage>> getChatHistory({
    required String contactWxId,
    int page = 0,
    int size = 20,
  }) async {
    if (_currentUserWxId == null) {
      if (kDebugMode) {
        print('❌ Current user WxId not set');
      }
      return [];
    }

    final response = await _messageService.getChatHistoryWithContact(
      currentUserWxId: _currentUserWxId!,
      contactWxId: contactWxId,
      page: page,
      size: size,
    );
    print(
      '✅ Chat history retrieved successfully ${response.data?.records?.length ?? 0}',
    );

    if (response.isSuccess && response.data != null) {
      return response.data!.records ?? [];
    } else {
      if (kDebugMode) {
        print('❌ Failed to get chat history: ${response.message}');
      }
      return [];
    }
  }

  /// 释放资源
  void dispose() {
    _messageSubscription?.cancel();
    _callbackSubscription?.cancel();
    _connectionSubscription?.cancel();
    _accountController.close();
    _unreadController.close();
    _websocketService.dispose();
    _notificationService.clearCache();
    _isInitialized = false;
  }
}
