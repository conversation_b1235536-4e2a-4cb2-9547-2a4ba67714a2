import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../config/app_config.dart';

/// WebSocket 连接状态
enum WebSocketStatus {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket 消息类型
class WebSocketMessage {
  final String type;
  final Map<String, dynamic> data;
  final String? id;
  final int timestamp;

  WebSocketMessage({
    required this.type,
    required this.data,
    this.id,
    int? timestamp,
  }) : timestamp = timestamp ?? DateTime.now().millisecondsSinceEpoch;

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: json['type'] as String,
      data: json['data'] as Map<String, dynamic>,
      id: json['id'] as String?,
      timestamp:
          json['timestamp'] as int? ?? DateTime.now().millisecondsSinceEpoch,
    );
  }

  Map<String, dynamic> toJson() => {
    'type': type,
    'data': data,
    if (id != null) 'id': id,
    'timestamp': timestamp,
  };
}

/// WebSocket 客户端
class WebSocketClient {
  static WebSocketClient? _instance;
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;

  // 状态管理
  WebSocketStatus _status = WebSocketStatus.disconnected;
  final StreamController<WebSocketStatus> _statusController =
      StreamController.broadcast();
  final StreamController<WebSocketMessage> _messageController =
      StreamController.broadcast();

  // 配置参数
  String? _token;
  String? _lastWxId;
  String? _lastChatroomId;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  static const Duration _reconnectDelay = Duration(seconds: 3);

  WebSocketClient._internal();

  static WebSocketClient get instance {
    _instance ??= WebSocketClient._internal();
    return _instance!;
  }

  /// 连接状态流
  Stream<WebSocketStatus> get statusStream => _statusController.stream;

  /// 消息流
  Stream<WebSocketMessage> get messageStream => _messageController.stream;

  /// 当前连接状态
  WebSocketStatus get status => _status;

  /// 是否已连接
  bool get isConnected => _status == WebSocketStatus.connected;

  /// 连接 WebSocket
  Future<void> connect({
    String? token,
    String? wxId,
    String? chatroomId,
  }) async {
    if (_status == WebSocketStatus.connecting ||
        _status == WebSocketStatus.connected) {
      return;
    }

    if (token != null) _token = token;
    _lastWxId = wxId ?? _lastWxId;
    _lastChatroomId = chatroomId ?? _lastChatroomId;
    _updateStatus(WebSocketStatus.connecting);

    try {
      final base = Uri.parse(AppConfig.websocketUrl);
      Uri uri = base;
      final effWxId = _lastWxId;
      final effChat = _lastChatroomId;
      final effToken = _token;
      final qp = Map<String, String>.from(base.queryParameters);
      if (effWxId != null && effWxId.isNotEmpty) {
        qp['wxId'] = effWxId;
      }
      if (effChat != null && effChat.isNotEmpty) {
        qp['chatroomId'] = effChat;
      }
      if (effToken != null && effToken.isNotEmpty) {
        qp['token'] = effToken;
      }
      if (qp.isNotEmpty) {
        uri = base.replace(queryParameters: qp);
      }

      _channel = WebSocketChannel.connect(uri, protocols: null);

      // 监听消息
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _updateStatus(WebSocketStatus.connected);
      _reconnectAttempts = 0;
      _startHeartbeat();

      if (kDebugMode) {
        print('🔌 WebSocket connected to $uri');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔌 WebSocket connection failed: $e');
      }
      _updateStatus(WebSocketStatus.error);
      _scheduleReconnect();
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    _stopHeartbeat();
    _stopReconnect();

    await _subscription?.cancel();
    await _channel?.sink.close(1000); // 1000 = normal closure

    _subscription = null;
    _channel = null;
    _updateStatus(WebSocketStatus.disconnected);

    if (kDebugMode) {
      print('🔌 WebSocket disconnected');
    }
  }

  /// 发送消息
  void sendMessage(WebSocketMessage message) {
    if (!isConnected) {
      if (kDebugMode) {
        print('🔌 Cannot send message: WebSocket not connected');
      }
      return;
    }

    try {
      final jsonString = jsonEncode(message.toJson());
      _channel?.sink.add(jsonString);

      if (kDebugMode) {
        print('🔌 Sent message: ${message.type}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔌 Failed to send message: $e');
      }
    }
  }

  /// 发送心跳
  void _sendHeartbeat() {
    sendMessage(
      WebSocketMessage(
        type: 'heartbeat',
        data: {'timestamp': DateTime.now().millisecondsSinceEpoch},
      ),
    );
  }

  /// 处理接收到的消息
  void _onMessage(dynamic data) {
    try {
      final dynamic decoded = jsonDecode(data as String);

      WebSocketMessage? message;
      if (decoded is Map<String, dynamic>) {
        // 兼容两种格式：
        // 1) 旧封包 { type: '...', data: {...} }
        // 2) 新后端直接推送的纯消息 { fromWxId: ..., toWxId: ..., ... }
        if (decoded.containsKey('type') && decoded.containsKey('data')) {
          message = WebSocketMessage.fromJson(decoded);
        } else {
          message = WebSocketMessage(type: 'gewe_message', data: decoded);
        }
      }

      if (message == null) {
        if (kDebugMode) {
          print('🔌 Unsupported message format: $decoded');
        }
        return;
      }

      if (kDebugMode) {
        print('🔌 Received message: ${message.type}');
      }

      // 处理心跳响应
      if (message.type == 'heartbeat_response') {
        return;
      }

      _messageController.add(message);
    } catch (e) {
      if (kDebugMode) {
        print('🔌 Failed to parse message: $e');
      }
    }
  }

  /// 处理连接错误
  void _onError(error) {
    if (kDebugMode) {
      print('🔌 WebSocket error: $error');
    }
    _updateStatus(WebSocketStatus.error);
    _scheduleReconnect();
  }

  /// 处理连接断开
  void _onDisconnected() {
    if (kDebugMode) {
      print('🔌 WebSocket disconnected');
    }
    _updateStatus(WebSocketStatus.disconnected);
    _scheduleReconnect();
  }

  /// 更新连接状态
  void _updateStatus(WebSocketStatus status) {
    if (_status != status) {
      _status = status;
      _statusController.add(status);
    }
  }

  /// 开始心跳
  void _startHeartbeat() {
    _stopHeartbeat();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (_) {
      _sendHeartbeat();
    });
  }

  /// 停止心跳
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      if (kDebugMode) {
        print('🔌 Max reconnect attempts reached');
      }
      return;
    }

    _stopReconnect();
    _updateStatus(WebSocketStatus.reconnecting);

    _reconnectTimer = Timer(_reconnectDelay, () {
      _reconnectAttempts++;
      if (kDebugMode) {
        print(
          '🔌 Reconnecting... (attempt $_reconnectAttempts/$_maxReconnectAttempts)',
        );
      }
      connect(token: _token, wxId: _lastWxId, chatroomId: _lastChatroomId);
    });
  }

  /// 停止重连
  void _stopReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// 释放资源
  void dispose() {
    disconnect();
    _statusController.close();
    _messageController.close();
  }
}
