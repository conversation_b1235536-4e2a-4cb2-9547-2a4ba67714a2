import '../models/api_response.dart';
import '../models/wechat_account.dart';
import '../network/base_service.dart';

class WechatAccountService extends BaseService {
  Future<ApiResponse<List<WechatAccount>>> getAccounts() async {
    return get<List<WechatAccount>>(
      '/wechat/accounts',
      fromJson: (json) => (json as List)
          .map((e) => WechatAccount.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

