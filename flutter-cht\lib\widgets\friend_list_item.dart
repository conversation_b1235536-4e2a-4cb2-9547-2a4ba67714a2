import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';

/// 好友列表项组件
class FriendListItem extends StatelessWidget {
  final GeWeFriend friend;
  final VoidCallback? onTap;
  final VoidCallback? onChatTap;
  final bool showChatButton;

  const FriendListItem({
    super.key,
    required this.friend,
    this.onTap,
    this.onChatTap,
    this.showChatButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: _buildAvatar(),
      title: _buildTitle(),
      subtitle: _buildSubtitle(),
      trailing: showChatButton ? _buildTrailing(context) : null,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 24,
      backgroundImage: friend.avatarUrl != null 
          ? NetworkImage(friend.avatarUrl!) 
          : null,
      backgroundColor: Colors.grey[300],
      child: friend.avatarUrl == null 
          ? Text(
              _getAvatarText(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            )
          : null,
    );
  }

  Widget _buildTitle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            friend.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (friend.isGroupChat)
          Container(
            margin: const EdgeInsets.only(left: 8),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Text(
              '群',
              style: TextStyle(
                fontSize: 10,
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSubtitle() {
    final List<String> subtitleParts = [];
    
    // 添加微信号
    subtitleParts.add('微信号: ${friend.wxId}');
    
    // 添加地区信息
    if (friend.province?.isNotEmpty == true || friend.city?.isNotEmpty == true) {
      final location = [friend.province, friend.city]
          .where((s) => s?.isNotEmpty == true)
          .join(' ');
      if (location.isNotEmpty) {
        subtitleParts.add('地区: $location');
      }
    }
    
    // 添加性别信息
    if (friend.sex != null && friend.sex! > 0) {
      subtitleParts.add('性别: ${friend.sexDescription}');
    }
    
    return Text(
      subtitleParts.join(' • '),
      style: TextStyle(
        fontSize: 12,
        color: Colors.grey[600],
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget? _buildTrailing(BuildContext context) {
    if (!showChatButton) return null;
    
    return IconButton(
      icon: const Icon(Icons.chat_bubble_outline),
      onPressed: onChatTap,
      tooltip: '开始聊天',
      iconSize: 20,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
    );
  }

  String _getAvatarText() {
    final name = friend.name;
    if (name.isEmpty) return '?';
    
    // 如果是中文，取最后一个字符
    if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(name)) {
      return name.substring(name.length - 1);
    }
    
    // 如果是英文，取第一个字符
    return name.substring(0, 1).toUpperCase();
  }
}
