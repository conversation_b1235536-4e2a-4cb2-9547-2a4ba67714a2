import 'package:flutter/material.dart';
import '../models/wechat_account.dart';

class AccountsSidebar extends StatefulWidget {
  final List<WechatAccount> accounts;
  final String? selectedWxId;
  final ValueChanged<WechatAccount> onSelect;

  const AccountsSidebar({
    super.key,
    required this.accounts,
    required this.selectedWxId,
    required this.onSelect,
  });

  @override
  State<AccountsSidebar> createState() => _AccountsSidebarState();
}

class _AccountsSidebarState extends State<AccountsSidebar> {
  bool _expanded = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(right: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          InkWell(
            onTap: () => setState(() => _expanded = !_expanded),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '微信账号',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Icon(_expanded ? Icons.expand_less : Icons.expand_more),
                ],
              ),
            ),
          ),
          if (_expanded)
            Expanded(
              child:
                  widget.accounts.isEmpty
                      ? const Center(child: Text('暂无账号'))
                      : ListView.separated(
                        itemCount: widget.accounts.length,
                        separatorBuilder:
                            (_, __) =>
                                Divider(height: 1, color: Colors.grey.shade300),
                        itemBuilder: (context, index) {
                          final a = widget.accounts[index];
                          final bool selected = a.wxId == widget.selectedWxId;
                          final title = a.nickname ?? a.remark ?? a.wxId;
                          final subtitle = [
                            a.wxId,
                            if (a.deviceName != null &&
                                a.deviceName!.isNotEmpty)
                              a.deviceName,
                            a.online ? '在线' : '离线',
                          ].whereType<String>().join(' · ');
                          return Material(
                            color: selected ? Colors.white : Colors.transparent,
                            child: ListTile(
                              dense: true,
                              selected: selected,
                              leading: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  CircleAvatar(
                                    radius: 18,
                                    backgroundImage:
                                        (a.smallHeadImgUrl ??
                                                    a.bigHeadImgUrl) !=
                                                null
                                            ? NetworkImage(
                                              a.smallHeadImgUrl ??
                                                  a.bigHeadImgUrl!,
                                            )
                                            : null,
                                    child:
                                        (a.smallHeadImgUrl ??
                                                    a.bigHeadImgUrl) ==
                                                null
                                            ? Text(
                                              title.isNotEmpty
                                                  ? title
                                                      .substring(0, 1)
                                                      .toUpperCase()
                                                  : '?',
                                            )
                                            : null,
                                  ),
                                  Positioned(
                                    right: -2,
                                    bottom: -2,
                                    child: Container(
                                      width: 10,
                                      height: 10,
                                      decoration: BoxDecoration(
                                        color:
                                            a.online
                                                ? Colors.green
                                                : Colors.grey,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              title: Text(
                                title,
                                overflow: TextOverflow.ellipsis,
                              ),
                              subtitle: Text(
                                subtitle,
                                style: TextStyle(
                                  color: a.online ? Colors.green : Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                              onTap: () => widget.onSelect(a),
                            ),
                          );
                        },
                      ),
            ),
          if (!_expanded)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                '共 ${widget.accounts.length} 个账号',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ),
        ],
      ),
    );
  }
}
