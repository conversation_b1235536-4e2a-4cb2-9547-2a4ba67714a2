import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_friend_service.dart';
import '../widgets/friend_list_item.dart';
import '../widgets/group_list_item.dart' show GroupTile;
import '../services/gewe_group_service.dart' show GroupListItem;
import '../widgets/loading_widget.dart';
import '../widgets/empty_widget.dart';

/// 搜索页面
class SearchPage extends StatefulWidget {
  final String? searchType; // 'friends', 'groups', 或 null (全部)

  const SearchPage({super.key, this.searchType});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final GeWeFriendService _friendService = GeWeFriendService();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  List<GeWeFriend> _searchResults = [];
  bool _isLoading = false;
  bool _hasSearched = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // 自动聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// 执行搜索
  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _searchResults.clear();
        _hasSearched = false;
        _errorMessage = '';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await _friendService.searchFriends(
        keyword: keyword.trim(),
      );

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          List<GeWeFriend> results = response.data!;

          // 根据搜索类型过滤结果
          if (widget.searchType == 'friends') {
            results = results.where((friend) => !friend.isGroupChat).toList();
          } else if (widget.searchType == 'groups') {
            results = results.where((friend) => friend.isGroupChat).toList();
          }

          setState(() {
            _searchResults = results;
            _isLoading = false;
            _hasSearched = true;
          });
        } else {
          setState(() {
            _searchResults.clear();
            _errorMessage = response.message;
            _isLoading = false;
            _hasSearched = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults.clear();
          _errorMessage = '搜索失败: $e';
          _isLoading = false;
          _hasSearched = true;
        });
      }
    }
  }

  /// 清除搜索
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults.clear();
      _hasSearched = false;
      _errorMessage = '';
    });
  }

  /// 打开详情页面
  void _openDetail(GeWeFriend item) {
    final routeName = item.isGroupChat ? '/group-detail' : '/friend-detail';
    Navigator.pushNamed(context, routeName, arguments: item);
  }

  /// 开始聊天
  void _startChat(GeWeFriend item) {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': item.wxId,
        'contactName': item.name,
        'isGroup': item.isGroupChat,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle()),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [_buildSearchBar(), Expanded(child: _buildBody())],
      ),
    );
  }

  String _getTitle() {
    switch (widget.searchType) {
      case 'friends':
        return '搜索好友';
      case 'groups':
        return '搜索群聊';
      default:
        return '搜索';
    }
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: _getSearchHint(),
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {}); // 更新清除按钮显示状态
              },
              onSubmitted: _performSearch,
              textInputAction: TextInputAction.search,
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed:
                _isLoading
                    ? null
                    : () => _performSearch(_searchController.text),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text('搜索'),
          ),
        ],
      ),
    );
  }

  String _getSearchHint() {
    switch (widget.searchType) {
      case 'friends':
        return '输入好友昵称或微信号';
      case 'groups':
        return '输入群聊名称或群号';
      default:
        return '输入昵称、微信号或群名';
    }
  }

  Widget _buildBody() {
    if (!_hasSearched) {
      return _buildSearchTips();
    }

    if (_isLoading) {
      return const LoadingWidget(message: '正在搜索...');
    }

    if (_errorMessage.isNotEmpty) {
      return EmptyWidget(
        icon: Icons.error_outline,
        title: '搜索失败',
        subtitle: _errorMessage,
        actionText: '重试',
        onAction: () => _performSearch(_searchController.text),
      );
    }

    if (_searchResults.isEmpty) {
      return EmptyWidget(
        icon: Icons.search_off,
        title: '未找到结果',
        subtitle: '请尝试其他关键词',
      );
    }

    return _buildSearchResults();
  }

  Widget _buildSearchTips() {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            '搜索提示',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '• 可以搜索好友昵称、备注或微信号\n'
            '• 可以搜索群聊名称或群号\n'
            '• 支持模糊搜索',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.separated(
      itemCount: _searchResults.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final item = _searchResults[index];

        if (item.isGroupChat) {
          return GroupTile(
            group: GroupListItem(
              chatroomId: item.wxId,
              nickName: item.nickName ?? item.remark,
              smallHeadImgUrl: item.smallHeadImgUrl,
              remark: item.remark,
              ownerWxid: null,
            ),
            onTap: () => _openDetail(item),
            onChatTap: () => _startChat(item),
          );
        } else {
          return FriendListItem(
            friend: item,
            onTap: () => _openDetail(item),
            onChatTap: () => _startChat(item),
          );
        }
      },
    );
  }
}
