package com.tourism.chat.wx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tourism.chat.group.entity.GeweGroup;
import com.tourism.chat.group.entity.GeweGroupMember;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.group.mapper.GeweGroupMapper;
import com.tourism.chat.group.mapper.GeweGroupMemberMapper;
import com.tourism.chat.group.mapper.GeweGroupMessageMapper;
import com.tourism.chat.tenant.TenantContextHolder;
import com.tourism.chat.wechat.entity.GeweWechatAcount;
import com.tourism.chat.wechat.service.GeweWechatAcountService;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.request.GeWeMessageCallbackRequest;
import com.tourism.chat.wx.service.GeWeMessageCallbackService;
import com.tourism.chat.wx.service.GeWeMessageHistoryService;
import com.tourism.chat.wx.service.GeWeApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * GeWe消息回调处理服务实现
 */
@Service
@Slf4j
public class GeWeMessageCallbackServiceImpl implements GeWeMessageCallbackService {

    @Resource
    private GeWeMessageHistoryService geWeMessageHistoryService;

    @Resource
    private com.tourism.chat.ws.WebSocketPushService webSocketPushService;

    @Resource
    private GeweWechatAcountService geweWechatAcountService;

    @Resource
    private GeweGroupMapper groupMapper;
    @Resource
    private GeweGroupMemberMapper groupMemberMapper;
    @Resource
    private GeweGroupMessageMapper groupMessageMapper;

    @Resource
    private GeWeApiService geWeApiService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void handleMessageCallback(GeWeMessageCallbackRequest request) {
        try {
            String wxid = request.getWxid();
            log.info("收到消息回调: typeName={}, msgId={}, fromUser(wx)=\"{}\", toUser=\"{}\", msgType={}",
                    request.getTypeName(), request.getMsgId(), request.getFromWxId(),
                    request.getToWxId(), request.getMsgType());
            GeweWechatAcount account = geweWechatAcountService.queryAccountByWxId(wxid);
            TenantContextHolder.setTenantId(account.getTenantId());

            // 先按 fromUserName 是否群聊判断
            if (isGroupChat(request)) {
                handleGroupMessage(request);
                return;
            }

            // 好友消息按原有流程保存到 gewe_message
            switch (request.getMsgType()) {
                case 1 -> handleTextMessage(request);
                case 3 -> handleImageMessage(request);
                case 34 -> handleVoiceMessage(request);
                case 43 -> handleVideoMessage(request);
                case 49 -> handleFileMessage(request);
                case 48 -> handleLocationMessage(request);
                case 47 -> handleEmojiMessage(request);
                case 10000 -> handleSystemMessage(request);
                default -> {
                    log.warn("未支持的消息类型: {}", request.getMsgType());
                    saveMessageToDatabase(request, request.getMsgType());
                }
            }

            // 推送好友消息到当前账号会话
            try {
                webSocketPushService.pushCallback(
                        request.getFromWxId(),
                        wxid /* 单聊推送到账号会话 */,
                        request.getMsgType(),
                        request.getContent(),
                        request.getCreateTime(),
                        request.getCreateTime());
            } catch (Exception ex) {
                log.error("WebSocket 推送失败", ex);
            }

        } catch (Exception e) {
            log.error("处理消息回调失败", e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    @Override
    public void handleTextMessage(GeWeMessageCallbackRequest request) {
        log.info("处理文本消息: {}", request.getContent());
        saveMessageToDatabase(request, 1);
    }

    @Override
    public void handleImageMessage(GeWeMessageCallbackRequest request) {
        log.info("处理图片消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 2);
    }

    @Override
    public void handleVoiceMessage(GeWeMessageCallbackRequest request) {
        log.info("处理语音消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 3);
    }

    @Override
    public void handleVideoMessage(GeWeMessageCallbackRequest request) {
        log.info("处理视频消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 4);
    }

    @Override
    public void handleFileMessage(GeWeMessageCallbackRequest request) {
        log.info("处理文件消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 5);
    }

    @Override
    public void handleLocationMessage(GeWeMessageCallbackRequest request) {
        log.info("处理位置消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 6);
    }

    @Override
    public void handleEmojiMessage(GeWeMessageCallbackRequest request) {
        log.info("处理表情消息: {}", request.getContent());
        saveMessageToDatabase(request, 7);
    }

    @Override
    public void handleLinkMessage(GeWeMessageCallbackRequest request) {
        log.info("处理链接消息: msgId={}, content={}", request.getMsgId(), request.getContent());
        saveMessageToDatabase(request, 8);
    }

    @Override
    public void handleSystemMessage(GeWeMessageCallbackRequest request) {
        log.info("处理系统消息: {}", request.getContent());
        saveMessageToDatabase(request, 9);
    }

    /**
     * 保存消息到数据库
     */
    private void saveMessageToDatabase(GeWeMessageCallbackRequest request, Integer messageType) {
        try {
            // 转换时间戳（GeWe API 返回秒级时间戳）
            LocalDateTime sendTime = null;
            if (request.getCreateTime() != null) {
                sendTime = LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(request.getCreateTime()),
                        ZoneId.systemDefault());
            }

            // 构建扩展数据
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("typeName", request.getTypeName());
            extraData.put("appid", request.getAppid());
            extraData.put("wxid", request.getWxid());
            extraData.put("pushContent", request.getPushContent());
            if (request.getData() != null) {
                extraData.put("msgSource", request.getData().getMsgSource());
                extraData.put("newMsgId", request.getData().getNewMsgId());
                extraData.put("msgSeq", request.getData().getMsgSeq());
                extraData.put("status", request.getData().getStatus());
                extraData.put("imgStatus", request.getData().getImgStatus());
            }

            String extraDataJson = extraData.isEmpty() ? null : objectMapper.writeValueAsString(extraData);

            // 判断是否为群聊消息（通过微信ID判断，群聊ID通常以@@开头）
            boolean isGroup = request.getToWxId() != null && request.getToWxId().startsWith("@@");

            // 保存到数据库
            GeWeMessage savedMessage = geWeMessageHistoryService.saveReceivedMessage(
                    request.getMsgId() != null ? request.getMsgId().toString() : null,
                    request.getFromWxId(),
                    request.getToWxId(),
                    request.getContent(),
                    messageType,
                    isGroup,
                    null, // GeWe API 暂时没有@列表信息
                    sendTime,
                    extraDataJson // 保存扩展数据
            );

            if (savedMessage != null) {
                log.info("消息保存成功，数据库ID: {}, 消息ID: {}", savedMessage.getId(), savedMessage.getMessageId());
            } else {
                log.warn("消息保存失败或消息已存在");
            }
        } catch (Exception e) {
            log.error("保存消息到数据库失败", e);
        }
    }

    /**
     * 是否为群聊消息：按 fromUserName 是否以 @chatroom 结尾判断
     */
    private boolean isGroupChat(GeWeMessageCallbackRequest req) {
        String from = req.getFromWxId();
        String toWxId = req.getToWxId();
        return (StringUtils.hasText(from) && (from.endsWith("@chatroom") || from.startsWith("@@"))) ||
                (StringUtils.hasText(toWxId) && (toWxId.endsWith("@chatroom") || toWxId.startsWith("@@")));
    }

    /**
     * 处理群聊消息：
     * 1) upsert 群记录
     * 2) 同步成员入库（若需要）
     * 3) 保存群聊消息到 gewe_group_message
     * 4) 通过 WebSocket 广播到该 chatroomId
     */
    private void handleGroupMessage(GeWeMessageCallbackRequest request) {
        String chatroomId = request.getFromWxId() ;
        String toWxId = request.getToWxId();
        if (!StringUtils.hasText(chatroomId)) {
            // 某些回调可能 from 是 chatroomId，to 是账号，这里兜底
            chatroomId = request.getFromWxId();
        }
        if (!StringUtils.hasText(chatroomId)) {
            log.warn("handleGroupMessage 缺少 chatroomId, req={}", request);
            return;
        }

        // 1) 群存在性检查与插入
        GeweGroup group = groupMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GeweGroup>()
                        .eq(GeweGroup::getChatroomId, chatroomId));
        LocalDateTime now = LocalDateTime.now();
        if (group == null) {
            // 通过 API 获取群信息，尽量补全
            com.tourism.chat.wx.model.GeWeChatroomModels.ChatroomInfoData info = geWeApiService
                    .getChatroomInfo(chatroomId);
            group = new GeweGroup();
            group.setChatroomId(chatroomId);
            if (info != null) {
                group.setNickName(info.getNickName());
                group.setOwnerWxid(info.getChatRoomOwner());
                group.setSmallHeadImgUrl(info.getSmallHeadImgUrl());
            }
            group.setCreateTime(now);
            groupMapper.insert(group);

            // 2) 同步成员
            try {
                com.tourism.chat.wx.model.GeWeChatroomModels.MemberListData ml = geWeApiService
                        .getChatroomMemberList(chatroomId);
                if (ml != null && ml.getMemberList() != null) {
                    for (com.tourism.chat.wx.model.GeWeChatroomModels.Member item : ml.getMemberList()) {
                        GeweGroupMember m = new GeweGroupMember();
                        m.setChatroomId(chatroomId);
                        m.setWxid(item.getWxid());
                        m.setNickName(item.getNickName());
                        m.setDisplayName(item.getDisplayName());
                        m.setInviterUserName(item.getInviterUserName());
                        m.setMemberFlag(item.getMemberFlag());
                        m.setSmallHeadImgUrl(item.getSmallHeadImgUrl());
                        m.setCreateTime(now);
                        groupMemberMapper.insert(m);
                    }
                }
            } catch (Exception ex) {
                log.warn("同步群成员失败: {}", chatroomId, ex);
            }
        }

        // 3) 保存群聊消息
        GeweGroupMessage gm = new GeweGroupMessage();
        gm.setChatroomId(chatroomId);
        gm.setSenderWxid(request.getContent().split(":")[0]);
        gm.setContent(request.getContent());
        gm.setMessageType(String.valueOf(request.getMsgType()));
        gm.setCreateTime(now);
        groupMessageMapper.insert(gm);

        // 4) WebSocket 推送
        try {
            Map<String, Object> payload = new HashMap<>();
            payload.put("fromWxId", gm.getSenderWxid());
            payload.put("toWxId", chatroomId);
            payload.put("messageType", request.getMsgType());
            payload.put("content", request.getContent());
            payload.put("sendTime", request.getCreateTime());
            payload.put("receiveTime", request.getCreateTime());
            String json = new ObjectMapper().writeValueAsString(payload);
            int sent = webSocketPushService.broadcastToChatroom(chatroomId, json);
            if (sent <= 0) {
                log.info("群聊 {} 无在线会话，推送暂存/忽略", chatroomId);
            }
        } catch (Exception e) {
            log.error("群聊消息 WebSocket 推送失败", e);
        }
    }

}
