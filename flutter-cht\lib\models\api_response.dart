import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// 统一的 API 响应模型
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  /// 响应状态码
  @Json<PERSON><PERSON>(name: 'code')
  final int code;

  /// 响应消息
  @Json<PERSON><PERSON>(name: 'message')
  final String message;

  /// 响应数据
  @JsonKey(name: 'data')
  final T? data;

  // /// 时间戳
  // @JsonKey(name: 'timestamp')
  // final int? timestamp;
  //
  // /// 请求追踪ID
  // @JsonKey(name: 'traceId')
  // final String? traceId;

  const ApiResponse({
    required this.code,
    required this.message,
    this.data,
    // this.timestamp,
    // this.traceId,
  });

  /// 是否成功
  /// 支持多种成功状态码：200 (HTTP标准) 和 0 (后端R类标准)
  bool get isSuccess => code == 200 || code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// 成功响应
  factory ApiResponse.success({
    required T data,
    String message = 'Success',
    int? timestamp,
    String? traceId,
  }) {
    return ApiResponse(
      code: 200,
      message: message,
      data: data,
      // timestamp: timestamp ?? DateTime.now().millisecondsSinceEpoch,
      // traceId: traceId,
    );
  }

  /// 失败响应
  factory ApiResponse.failure({
    required int code,
    required String message,
    T? data,
    int? timestamp,
    String? traceId,
  }) {
    return ApiResponse(
      code: code,
      message: message,
      data: data,
      // timestamp: timestamp ?? DateTime.now().millisecondsSinceEpoch,
      // traceId: traceId,
    );
  }

  @override
  String toString() {
    /*, timestamp: $timestamp, traceId: $traceId}*/
    return 'ApiResponse{code: $code, message: $message, data: $data';
  }
}

/// 分页响应模型
@JsonSerializable(genericArgumentFactories: true)
class PageResponse<T> {
  /// 当前页码
  @JsonKey(name: 'current')
  final int? current;

  /// 每页大小
  @JsonKey(name: 'size')
  final int? size;

  /// 总记录数
  @JsonKey(name: 'total')
  final int? total;

  /// 总页数
  @JsonKey(name: 'pages')
  final int? pages;

  /// 数据列表
  @JsonKey(name: 'records')
  final List<T>? records;

  const PageResponse({
    this.current,
    this.size,
    this.total,
    this.pages,
    this.records,
  });

  /// 是否有下一页
  bool get hasNext => (current ?? 0) < (pages ?? 1) - 1;

  /// 是否有上一页
  bool get hasPrevious => (current ?? 0) > 0;

  /// 是否为空
  bool get isEmpty => records?.isEmpty ?? true;

  /// 是否不为空
  bool get isNotEmpty => records?.isNotEmpty ?? false;

  /// 泛型 fromJson
  factory PageResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$PageResponseFromJson(json, fromJsonT);

  /// 泛型 toJson
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PageResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'PageResponse{current: $current, size: $size, total: $total, pages: $pages, records: ${records?.length ?? 0} items}';
  }
}
