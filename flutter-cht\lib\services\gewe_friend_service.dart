import 'package:im_app/services/gewe_service_manager.dart';

import '../models/api_response.dart';
import '../models/gewe_friend.dart';
import '../network/base_service.dart';

/// GeWe 好友管理服务
class GeWeFriendService extends BaseService {
  /// 同步好友信息
  /// [wxIds] 可选，指定要同步的微信ID数组，为空则同步所有好友
  Future<ApiResponse<SyncFriendsResponse>> syncFriends({
    List<String>? wxIds,
  }) async {
    final request = wxIds != null ? SyncFriendsRequest(wxIds: wxIds) : null;

    return await post<SyncFriendsResponse>(
      '/gewe/friend/sync',
      data: request?.toJson(),
      fromJson:
          (json) => SyncFriendsResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 获取好友列表（不包含群聊）
  Future<ApiResponse<List<GeWeFriend>>> getFriendList({String? wxId}) async {
    final path =
        (wxId == null || wxId.isEmpty)
            ? '/gewe/friend/list'
            : '/gewe/friend/listByWxId';
    return await get<List<GeWeFriend>>(
      path,
      queryParameters: (wxId == null || wxId.isEmpty) ? null : {'wxId': wxId},
      fromJson:
          (json) =>
              (json as List)
                  .map(
                    (item) => GeWeFriend.fromJson(item as Map<String, dynamic>),
                  )
                  .toList(),
    );
  }

  /// 获取群聊列表
  Future<ApiResponse<List<GeWeFriend>>> getGroupList() async {
    return await get<List<GeWeFriend>>(
      '/gewe/friend/groups',
      fromJson:
          (json) =>
              (json as List)
                  .map(
                    (item) => GeWeFriend.fromJson(item as Map<String, dynamic>),
                  )
                  .toList(),
    );
  }

  /// 搜索好友
  /// [keyword] 搜索关键词（昵称或备注）
  Future<ApiResponse<List<GeWeFriend>>> searchFriends({String? keyword}) async {
    return await get<List<GeWeFriend>>(
      '/gewe/friend/search',
      queryParameters: keyword != null ? {'keyword': keyword} : null,
      fromJson:
          (json) =>
              (json as List)
                  .map(
                    (item) => GeWeFriend.fromJson(item as Map<String, dynamic>),
                  )
                  .toList(),
    );
  }

  /// 获取好友详情
  /// [wxId] 微信ID
  Future<ApiResponse<GeWeFriend>> getFriendDetail(String wxId) async {
    return await get<GeWeFriend>(
      '/gewe/friend/$wxId',
      fromJson: (json) => GeWeFriend.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 刷新好友信息
  /// [wxId] 微信ID
  Future<ApiResponse<Map<String, dynamic>>> refreshFriend(String wxId) async {
    return await post<Map<String, dynamic>>(
      '/gewe/friend/$wxId/refresh',
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }

  /// 获取所有联系人（好友 + 群聊）
  Future<ApiResponse<List<GeWeFriend>>> getAllContacts() async {
    try {
      // 并发获取好友和群聊列表
      final results = await Future.wait([getFriendList(), getGroupList()]);

      final friendsResponse = results[0];
      final groupsResponse = results[1];

      if (friendsResponse.isSuccess && groupsResponse.isSuccess) {
        final allContacts = <GeWeFriend>[
          ...friendsResponse.data!,
          ...groupsResponse.data!,
        ];

        return ApiResponse.success(data: allContacts);
      } else {
        final errorMessage =
            friendsResponse.isFailure
                ? friendsResponse.message
                : groupsResponse.message;
        return ApiResponse.failure(code: -1, message: '获取联系人失败: $errorMessage');
      }
    } catch (e) {
      return ApiResponse.failure(code: -1, message: '获取联系人失败: $e');
    }
  }

  /// 根据微信ID批量获取好友信息
  /// [wxIds] 微信ID列表
  Future<ApiResponse<List<GeWeFriend>>> getFriendsByWxIds(
    List<String> wxIds,
  ) async {
    try {
      final futures = wxIds.map((wxId) => getFriendDetail(wxId));
      final results = await Future.wait(futures);

      final friends = <GeWeFriend>[];
      for (final result in results) {
        if (result.isSuccess && result.data != null) {
          friends.add(result.data!);
        }
      }

      return ApiResponse.success(data: friends);
    } catch (e) {
      return ApiResponse.failure(code: -1, message: '批量获取好友信息失败: $e');
    }
  }
}
