import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_friend_service.dart';

/// 群聊详情页面
class GroupDetailPage extends StatefulWidget {
  final GeWeFriend group;

  const GroupDetailPage({super.key, required this.group});

  @override
  State<GroupDetailPage> createState() => _GroupDetailPageState();
}

class _GroupDetailPageState extends State<GroupDetailPage> {
  final GeWeFriendService _friendService = GeWeFriendService();

  late GeWeFriend _group;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _group = widget.group;
  }

  /// 刷新群聊信息
  Future<void> _refreshGroupInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _friendService.refreshFriend(_group.wxId);
      if (response.isSuccess) {
        // 重新获取群聊详情
        final detailResponse = await _friendService.getFriendDetail(
          _group.wxId,
        );
        if (detailResponse.isSuccess && detailResponse.data != null) {
          setState(() {
            _group = detailResponse.data!;
          });

          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('群聊信息已更新')));
        }
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('刷新失败: ${response.message}')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('刷新失败: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 进入群聊
  void _enterGroupChat() {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': _group.wxId,
        'contactName': _group.name,
        'isGroup': true,
      },
    );
  }

  /// 复制群号
  void _copyGroupId() {
    Clipboard.setData(ClipboardData(text: _group.wxId));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('群号已复制到剪贴板')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('群聊详情'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshGroupInfo,
            tooltip: '刷新信息',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(),
            _buildInfoSection(),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundImage:
                _group.avatarUrl != null
                    ? NetworkImage(_group.avatarUrl!)
                    : null,
            backgroundColor: Colors.green[300],
            child:
                _group.avatarUrl == null
                    ? const Icon(Icons.group, color: Colors.white, size: 50)
                    : null,
          ),
          const SizedBox(height: 16),
          Text(
            _group.name,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '群聊',
              style: TextStyle(
                fontSize: 12,
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (_group.signature?.isNotEmpty == true) ...[
            const SizedBox(height: 12),
            Text(
              _group.signature!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildInfoItem(
            icon: Icons.group,
            title: '群号',
            content: _group.wxId,
            onTap: _copyGroupId,
            showCopy: true,
          ),
          if (_group.alias?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.alternate_email,
              title: '群别名',
              content: _group.alias!,
            ),
          if (_group.remark?.isNotEmpty == true)
            _buildInfoItem(
              icon: Icons.edit,
              title: '群备注',
              content: _group.remark!,
            ),
          _buildInfoItem(icon: Icons.info, title: '群类型', content: '微信群聊'),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String content,
    VoidCallback? onTap,
    bool showCopy = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.green),
      title: Text(title),
      subtitle: Text(content),
      trailing:
          showCopy
              ? IconButton(
                icon: const Icon(Icons.copy, size: 20),
                onPressed: onTap,
                tooltip: '复制',
              )
              : null,
      onTap: onTap,
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _enterGroupChat,
              icon: const Icon(Icons.chat),
              label: const Text('进入群聊'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 16),
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/group-members',
                      arguments: {
                        'chatroomId': _group.wxId,
                        'title': '${_group.name} 的成员',
                      },
                    );
                  },
                  icon: const Icon(Icons.people),
                  label: const Text('群成员'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: 实现群设置功能
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('群设置功能开发中...')),
                    );
                  },
                  icon: const Icon(Icons.settings),
                  label: const Text('群设置'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: 实现群公告功能
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('群公告功能开发中...')));
              },
              icon: const Icon(Icons.announcement),
              label: const Text('群公告'),
            ),
          ),
        ],
      ),
    );
  }
}
