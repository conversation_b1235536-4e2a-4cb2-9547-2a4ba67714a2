import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import '../constants/socket_events.dart';
import '../models/chat_message.dart';
import '../models/system_message.dart';
import '../models/user_info.dart';
import '../services/socket_auth_manager.dart';
import '../services/socket_service.dart';

/// Socket.IO 测试页面 - 启动后自动连接
class SocketTestPage extends StatefulWidget {
  const SocketTestPage({super.key});

  @override
  State<SocketTestPage> createState() => _SocketTestPageState();
}

class _SocketTestPageState extends State<SocketTestPage> {
  final SocketService _socketService = SocketService();
  final SocketAuthManager _authManager = SocketAuthManager();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<String> _messages = [];
  bool _isConnected = false;
  bool _isLoading = true;
  UserInfo? _currentUser;

  late StreamSubscription _groupMessageSub;
  late StreamSubscription _privateMessageSub;
  late StreamSubscription _systemMessageSub;
  late StreamSubscription _connectionSub;

  @override
  void initState() {
    super.initState();
    _setupEventListeners();
    _autoConnect();
  }

  @override
  void dispose() {
    _groupMessageSub.cancel();
    _privateMessageSub.cancel();
    _systemMessageSub.cancel();
    _connectionSub.cancel();
    _socketService.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 设置事件监听器
  void _setupEventListeners() {
    // 监听群聊消息
    _groupMessageSub = _socketService.groupMessageStream.listen((message) {
      _addMessage('📩 群聊消息: ${message.content} (来自: ${message.fromUserId})');
    });

    // 监听私聊消息
    _privateMessageSub = _socketService.privateMessageStream.listen((message) {
      _addMessage('💬 私聊消息: ${message.content} (来自: ${message.fromUserId})');
    });

    // 监听系统消息
    _systemMessageSub = _socketService.systemMessageStream.listen((message) {
      _addMessage('🔔 系统消息: ${message.type} - ${message.content}');
    });

    // 监听连接状态
    _connectionSub = _socketService.connectionStream.listen((isConnected) {
      setState(() {
        _isConnected = isConnected;
      });
      _addMessage(_isConnected ? '✅ Socket.IO 已连接' : '❌ Socket.IO 已断开');
    });
  }

  /// 自动连接
  Future<void> _autoConnect() async {
    try {
      _addMessage('🚀 开始自动连接...');
      
      // 初始化认证管理器
      await _authManager.init(baseUrl: 'http://localhost:18080/api');
      _addMessage('✅ 认证管理器初始化完成');

      // 自动登录
      final userInfo = await _authManager.login(
        phone: '13800138000',
        tenantId: '1',
        password: 'password123',
      );

      if (userInfo != null) {
        _currentUser = userInfo;
        _addMessage('✅ 登录成功: ${userInfo.username} (ID: ${userInfo.id})');

        // 获取有效token
        final token = _authManager.getValidToken();
        if (token != null) {
          // 连接Socket.IO
          await _socketService.connect(
            serverUrl: 'http://localhost:19092',
            jwtToken: token,
            userInfo: userInfo,
          );
          _addMessage('🔗 Socket.IO 连接请求已发送');
        } else {
          _addMessage('❌ Token无效');
        }
      } else {
        _addMessage('❌ 登录失败');
      }
    } catch (e) {
      _addMessage('❌ 连接异常: $e');
      developer.log('自动连接失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 添加消息到列表
  void _addMessage(String message) {
    setState(() {
      _messages.add('[${DateTime.now().toString().substring(11, 19)}] $message');
    });
    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 发送群聊消息
  void _sendGroupMessage() {
    if (_messageController.text.isEmpty) return;
    
    final content = _messageController.text;
    _socketService.sendGroupMessage(
      groupId: 'test_group_123',
      content: content,
    );
    
    _addMessage('📤 发送群聊消息: $content');
    _messageController.clear();
  }

  /// 发送私聊消息
  void _sendPrivateMessage() {
    if (_messageController.text.isEmpty) return;
    
    final content = _messageController.text;
    _socketService.sendPrivateMessage(
      toUserId: '2',
      content: content,
    );
    
    _addMessage('📤 发送私聊消息: $content');
    _messageController.clear();
  }

  /// 断开连接
  void _disconnect() {
    _socketService.disconnect();
    _addMessage('🔌 手动断开连接');
  }

  /// 重新连接
  void _reconnect() {
    _autoConnect();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Socket.IO 测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          // 连接状态指示器
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: Row(
              children: [
                Icon(
                  _isConnected ? Icons.wifi : Icons.wifi_off,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(_isConnected ? '已连接' : '未连接'),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 用户信息和控制按钮
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_currentUser != null)
                  Text(
                    '当前用户: ${_currentUser!.username} (ID: ${_currentUser!.id}, 租户: ${_currentUser!.tenantId})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: _isLoading ? null : _reconnect,
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                      child: const Text('重连', style: TextStyle(color: Colors.white)),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _isConnected ? _disconnect : null,
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                      child: const Text('断开', style: TextStyle(color: Colors.white)),
                    ),
                    const SizedBox(width: 16),
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('连接中...'),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          // 消息列表
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.symmetric(vertical: 2),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _messages[index],
                      style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // 消息输入区域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: '输入消息内容...',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    onSubmitted: (_) => _sendGroupMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isConnected ? _sendGroupMessage : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                  child: const Text('群聊', style: TextStyle(color: Colors.white)),
                ),
                const SizedBox(width: 4),
                ElevatedButton(
                  onPressed: _isConnected ? _sendPrivateMessage : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                  child: const Text('私聊', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
