package com.tourism.chat.socket.config;

import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.Transport;
import com.tourism.chat.socket.auth.SocketJwtAuthInterceptor;
import org.springframework.context.annotation.Bean;

import com.corundumstudio.socketio.Configuration;

@org.springframework.context.annotation.Configuration
public class SocketIoConfig {

    private final SocketJwtAuthInterceptor jwtAuthInterceptor;

    public SocketIoConfig(SocketJwtAuthInterceptor jwtAuthInterceptor) {
        this.jwtAuthInterceptor = jwtAuthInterceptor;
    }

    @Bean
    public SocketIOServer socketIOServer() {
        Configuration config = new Configuration();
        config.setHostname("0.0.0.0");
        config.setPort(19092);

        // 支持 WebSocket + 轮询
        config.setTransports(Transport.WEBSOCKET, Transport.POLLING);

        // 心跳 & 超时
        config.setPingInterval(25000);
        config.setPingTimeout(60000);

        // 设置JWT认证拦截器
        config.setAuthorizationListener(jwtAuthInterceptor);

        // 允许跨域
        config.setOrigin("*");

        return new SocketIOServer(config);
    }
}
