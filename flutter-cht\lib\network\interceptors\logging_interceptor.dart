import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 日志拦截器
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      print('📤 REQUEST[${options.method}] => PATH: ${options.uri}');
      print('📤 Headers: ${options.headers}');
      if (options.queryParameters.isNotEmpty) {
        print('📤 Query Parameters: ${options.queryParameters}');
      }
      if (options.data != null) {
        print('📤 Body: ${_formatData(options.data)}');
      }
      print('📤 ────────────────────────────────────────────────────────────────');
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('📥 RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.uri}');
      print('📥 Headers: ${response.headers}');
      print('📥 Body: ${_formatData(response.data)}');
      print('📥 ────────────────────────────────────────────────────────────────');
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('❌ ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.uri}');
      print('❌ Type: ${err.type}');
      print('❌ Message: ${err.message}');
      if (err.response != null) {
        print('❌ Response Headers: ${err.response?.headers}');
        print('❌ Response Body: ${_formatData(err.response?.data)}');
      }
      print('❌ ────────────────────────────────────────────────────────────────');
    }
    super.onError(err, handler);
  }

  /// 格式化数据用于日志输出
  String _formatData(dynamic data) {
    if (data == null) return 'null';
    
    if (data is String) {
      // 限制字符串长度，避免日志过长
      if (data.length > 1000) {
        return '${data.substring(0, 1000)}... (truncated)';
      }
      return data;
    }
    
    if (data is Map || data is List) {
      final jsonString = data.toString();
      // 限制 JSON 字符串长度
      if (jsonString.length > 1000) {
        return '${jsonString.substring(0, 1000)}... (truncated)';
      }
      return jsonString;
    }
    
    return data.toString();
  }
}
