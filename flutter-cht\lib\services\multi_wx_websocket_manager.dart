import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../config/app_config.dart';
import '../models/gewe_message.dart';

/// 多微信号 WebSocket 管理器：为每个 wxId 建立一个 WebSocket 连接
class MultiWxWebSocketManager {
  final Map<String, WebSocketChannel> _channels = {};
  final Map<String, StreamSubscription> _subs = {};

  // 汇总的消息流
  final StreamController<GeWeMessage> _messageController =
      StreamController.broadcast();

  Stream<GeWeMessage> get messageStream => _messageController.stream;

  bool isConnected(String wxId) => _channels[wxId] != null;

  Future<void> connectAll(Iterable<String> wxIds) async {
    for (final wxId in wxIds) {
      if (wxId.isEmpty || _channels.containsKey(wxId)) continue;
      final uri = _buildUri(wxId);
      try {
        final channel = WebSocketChannel.connect(uri);
        _channels[wxId] = channel;
        _subs[wxId] = channel.stream.listen(
          (event) => _onMessage(wxId, event),
          onError: (e) => _onError(wxId, e),
          onDone: () => _onDone(wxId),
        );
        if (kDebugMode) {
          print('🔌 [$wxId] WS connected: $uri');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ [$wxId] WS connect failed: $e');
        }
      }
    }
  }

  Future<void> disconnectAll() async {
    for (final wxId in _channels.keys.toList()) {
      await disconnect(wxId);
    }
  }

  Future<void> disconnect(String wxId) async {
    try {
      await _subs[wxId]?.cancel();
      await _channels[wxId]?.sink.close(1000);
    } catch (_) {}
    _subs.remove(wxId);
    _channels.remove(wxId);
    if (kDebugMode) {
      print('🔌 [$wxId] WS disconnected');
    }
  }

  Uri _buildUri(String wxId) {
    final base = Uri.parse(AppConfig.websocketUrl);
    final qp = Map<String, String>.from(base.queryParameters);
    qp['wxId'] = wxId;
    return base.replace(queryParameters: qp);
  }

  void _onMessage(String wxId, dynamic data) {
    try {
      final decoded = jsonDecode(data as String) as Map<String, dynamic>;
      // 后端直接推原始 JSON
      final msg = _adaptBackendPushToGeWeMessage(decoded);
      _messageController.add(msg);
    } catch (e) {
      if (kDebugMode) {
        print('❌ [$wxId] parse WS message error: $e');
      }
    }
  }

  void _onError(String wxId, dynamic error) {
    if (kDebugMode) {
      print('❌ [$wxId] WS error: $error');
    }
  }

  void _onDone(String wxId) {
    if (kDebugMode) {
      print('ℹ️ [$wxId] WS closed');
    }
  }

  GeWeMessage _adaptBackendPushToGeWeMessage(Map<String, dynamic> data) {
    final String? fromWxId = data['fromWxId'] as String?;
    final String? toWxId = data['toWxId'] as String?;
    final dynamic mt = data['messageType'];
    final String? content = data['content'] as String?;
    final dynamic atWxIds = data['atWxIds'];
    final dynamic sendTime = data['sendTime'];
    final dynamic receiveTime = data['receiveTime'];

    final int? msgTypeInt = _mapMessageTypeToInt(mt);
    final DateTime? sendDt = _dateTimeFromSeconds(sendTime);
    final DateTime? recvDt = _dateTimeFromSeconds(receiveTime);

    String? atJoined;
    if (atWxIds is List) {
      atJoined = atWxIds
          .map((e) => e?.toString() ?? '')
          .where((e) => e.isNotEmpty)
          .join(',');
    } else if (atWxIds is String) {
      atJoined = atWxIds;
    }

    final bool isGroup =
        (toWxId?.endsWith('@chatroom') == true) || (toWxId?.startsWith('@@') == true);

    return GeWeMessage(
      msgId: null,
      fromWxId: fromWxId,
      toWxId: toWxId,
      content: content,
      msgType: msgTypeInt,
      sendTime: sendDt,
      receiveTime: recvDt,
      isGroup: isGroup,
      isSent: false,
      atWxIds: atJoined,
    );
  }

  int? _mapMessageTypeToInt(dynamic mt) {
    if (mt == null) return null;
    if (mt is num) return mt.toInt();
    final s = mt.toString().toLowerCase();
    switch (s) {
      case 'text':
        return 1;
      case 'image':
        return 3;
      case 'voice':
        return 34;
      case 'video':
        return 43;
      case 'file':
        return 49;
      case 'location':
        return 48;
      case 'emoji':
        return 47;
      case 'system':
        return 10000;
      default:
        return null;
    }
  }

  DateTime? _dateTimeFromSeconds(dynamic v) {
    if (v == null) return null;
    try {
      final int seconds = v is num ? v.toInt() : int.parse(v.toString());
      return DateTime.fromMillisecondsSinceEpoch(seconds * 1000, isUtc: false);
    } catch (_) {
      return null;
    }
  }

  void dispose() {
    disconnectAll();
    _messageController.close();
  }
}

