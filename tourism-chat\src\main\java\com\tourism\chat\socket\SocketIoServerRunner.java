package com.tourism.chat.socket;

import com.corundumstudio.socketio.BroadcastOperations;
import com.corundumstudio.socketio.SocketIOServer;
import com.tourism.chat.constants.RedisKeyConstants;
import com.tourism.chat.socket.auth.SocketJwtAuthInterceptor;
import com.tourism.chat.socket.redis.MessagePublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SocketIoServerRunner implements CommandLineRunner {

    private final SocketIOServer server;
    private final StringRedisTemplate redisTemplate;
    private final MessagePublisher messagePublisher;

    public SocketIoServerRunner(SocketIOServer server, StringRedisTemplate redisTemplate, MessagePublisher messagePublisher) {
        this.server = server;
        this.redisTemplate = redisTemplate;
        this.messagePublisher = messagePublisher;
    }

    @Override
    public void run(String... args) {
        // 客户端连接
        server.addConnectListener(client -> {
            // JWT认证已在SocketJwtAuthInterceptor中完成
            // 直接从握手数据中获取认证后的用户信息
            String tenantId = SocketJwtAuthInterceptor.getTenantId(client.getHandshakeData());
            String userId = SocketJwtAuthInterceptor.getUserId(client.getHandshakeData());
            String username = SocketJwtAuthInterceptor.getUsername(client.getHandshakeData());
            String phone = SocketJwtAuthInterceptor.getPhone(client.getHandshakeData());
            String transport = client.getTransport().name(); // WS or POLLING

            // 这些信息已经在JWT认证时验证过，不应该为null
            if (tenantId == null || userId == null) {
                log.error("❌ 连接失败: 用户信息缺失 tenantId={}, userId={}", tenantId, userId);
                client.disconnect();
                return;
            }

            // 在线用户写入 Redis
            redisTemplate.opsForValue().set(RedisKeyConstants.Online.userKey(tenantId, userId.toString()), client.getSessionId().toString());

            // 加入房间
            // 1. 租户维度
            client.joinRoom(RedisKeyConstants.Room.tenantRoom(tenantId));
            // 2. 用户维度
            client.joinRoom(RedisKeyConstants.Room.userRoom(tenantId, userId.toString()));
            // 3. 微信维度 (这里应该是wxId，暂时用userId代替)
            client.joinRoom(RedisKeyConstants.Room.wxRoom(tenantId, userId.toString()));

            // 发布用户上线通知
            messagePublisher.publishUserOnline(tenantId, userId.toString());

            log.info("✅ 用户连接成功: tenant={}, user={}, username={}, phone={}, sessionId={}, transport={}",
                    tenantId, userId, username, phone, client.getSessionId(), transport);
        });

        // 客户端断开
        server.addDisconnectListener(client -> {
            String tenantId = SocketJwtAuthInterceptor.getTenantId(client.getHandshakeData());
            String userId = SocketJwtAuthInterceptor.getUserId(client.getHandshakeData());
            String username = SocketJwtAuthInterceptor.getUsername(client.getHandshakeData());

            if (tenantId != null && userId != null) {
                redisTemplate.delete(RedisKeyConstants.Online.userKey(tenantId, userId.toString()));

                // 发布用户下线通知
                messagePublisher.publishUserOffline(tenantId, userId.toString());
            }
            log.info("❌ 用户断开连接: tenant={}, user={}, username={}, sessionId={}",
                    tenantId, userId, username, client.getSessionId());
        });

        // 群聊消息
        server.addEventListener(RedisKeyConstants.Event.GROUP_MESSAGE, ChatMessage.class, (client, data, ackSender) -> {
            String room = RedisKeyConstants.Room.groupRoom(data.getTenantId(), data.getGroupId());
            BroadcastOperations roomOperations = server.getRoomOperations(room);
            roomOperations.sendEvent(RedisKeyConstants.Event.GROUP_MESSAGE, data);
            // 使用消息发布器发布到 Redis，让其他实例也能收到
            messagePublisher.publishGroupMessage(data);
            log.info("📩 群聊消息: {}", data);
        });

        // 私聊消息
        server.addEventListener(RedisKeyConstants.Event.PRIVATE_MESSAGE, ChatMessage.class, (client, data, ackSender) -> {
            String room = RedisKeyConstants.Room.userRoom(data.getTenantId(), data.getToUserId());
            server.getRoomOperations(room).sendEvent(RedisKeyConstants.Event.PRIVATE_MESSAGE, data);
            // 使用消息发布器发布到 Redis
            messagePublisher.publishPrivateMessage(data);
            log.info("📩 私聊消息: {}", data);
        });

        server.start();
        System.out.println("🚀 Socket.IO 服务已启动 ws://localhost:19092");
        Runtime.getRuntime().addShutdownHook(new Thread(server::stop));
    }


}
