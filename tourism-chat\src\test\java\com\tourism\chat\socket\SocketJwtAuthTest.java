package com.tourism.chat.socket;

import com.corundumstudio.socketio.HandshakeData;
import com.tourism.chat.auth.JwtTokenProvider;
import com.tourism.chat.entity.User;
import com.tourism.chat.socket.auth.SocketJwtAuthInterceptor;
import io.netty.handler.codec.http.HttpHeaders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SocketIO JWT认证测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class SocketJwtAuthTest {

    @Mock
    private JwtTokenProvider jwtTokenProvider;

    @Mock
    private HandshakeData handshakeData;

    @Mock
    private HttpHeaders httpHeaders;

    private SocketJwtAuthInterceptor authInterceptor;
    private User testUser;
    private String validToken;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        authInterceptor = new SocketJwtAuthInterceptor(jwtTokenProvider);
        
        // 创建测试用户
        testUser = new User();
        testUser.setId(123L);
        testUser.setName("testuser");
        testUser.setPhone("13800138000");
        testUser.setTenantId(1L);
        
        validToken = "valid.jwt.token";
    }

    @Test
    void testValidJwtTokenInHeader() {
        // 准备测试数据
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn("Bearer " + validToken);
        when(jwtTokenProvider.validateToken(validToken)).thenReturn(true);
        
        // Mock JWT claims
        io.jsonwebtoken.Claims claims = mock(io.jsonwebtoken.Claims.class);
        when(jwtTokenProvider.parseClaims(validToken)).thenReturn(claims);
        when(claims.get("id", Long.class)).thenReturn(123L);
        when(claims.get("tenantId", Long.class)).thenReturn(1L);
        when(claims.get("phone", String.class)).thenReturn("13800138000");
        when(claims.getSubject()).thenReturn("testuser");
        
        // Mock URL参数存储
        Map<String, List<String>> urlParams = new HashMap<>();
        when(handshakeData.getUrlParams()).thenReturn(urlParams);
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertTrue(result);
        verify(jwtTokenProvider).validateToken(validToken);
        verify(jwtTokenProvider).parseClaims(validToken);
    }

    @Test
    void testValidJwtTokenInUrlParam() {
        // 准备测试数据 - Authorization头为空，token在URL参数中
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn(null);
        when(handshakeData.getSingleUrlParam("token")).thenReturn(validToken);
        when(jwtTokenProvider.validateToken(validToken)).thenReturn(true);
        
        // Mock JWT claims
        io.jsonwebtoken.Claims claims = mock(io.jsonwebtoken.Claims.class);
        when(jwtTokenProvider.parseClaims(validToken)).thenReturn(claims);
        when(claims.get("id", Long.class)).thenReturn(123L);
        when(claims.get("tenantId", Long.class)).thenReturn(1L);
        when(claims.get("phone", String.class)).thenReturn("13800138000");
        when(claims.getSubject()).thenReturn("testuser");
        
        // Mock URL参数存储
        Map<String, List<String>> urlParams = new HashMap<>();
        when(handshakeData.getUrlParams()).thenReturn(urlParams);
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertTrue(result);
        verify(jwtTokenProvider).validateToken(validToken);
    }

    @Test
    void testNoTokenProvided() {
        // 准备测试数据 - 没有提供token
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn(null);
        when(handshakeData.getSingleUrlParam("token")).thenReturn(null);
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertFalse(result);
        verify(jwtTokenProvider, never()).validateToken(any());
    }

    @Test
    void testInvalidToken() {
        // 准备测试数据 - 无效token
        String invalidToken = "invalid.jwt.token";
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn("Bearer " + invalidToken);
        when(jwtTokenProvider.validateToken(invalidToken)).thenReturn(false);
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertFalse(result);
        verify(jwtTokenProvider).validateToken(invalidToken);
        verify(jwtTokenProvider, never()).parseClaims(any());
    }

    @Test
    void testTokenWithoutBearerPrefix() {
        // 准备测试数据 - token没有Bearer前缀
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn(validToken); // 直接是token，没有Bearer前缀
        when(jwtTokenProvider.validateToken(validToken)).thenReturn(true);
        
        // Mock JWT claims
        io.jsonwebtoken.Claims claims = mock(io.jsonwebtoken.Claims.class);
        when(jwtTokenProvider.parseClaims(validToken)).thenReturn(claims);
        when(claims.get("id", Long.class)).thenReturn(123L);
        when(claims.get("tenantId", Long.class)).thenReturn(1L);
        when(claims.get("phone", String.class)).thenReturn("13800138000");
        when(claims.getSubject()).thenReturn("testuser");
        
        // Mock URL参数存储
        Map<String, List<String>> urlParams = new HashMap<>();
        when(handshakeData.getUrlParams()).thenReturn(urlParams);
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertTrue(result);
        verify(jwtTokenProvider).validateToken(validToken);
    }

    @Test
    void testTokenMissingRequiredClaims() {
        // 准备测试数据 - token缺少必要的claims
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn("Bearer " + validToken);
        when(jwtTokenProvider.validateToken(validToken)).thenReturn(true);
        
        // Mock JWT claims - 缺少userId
        io.jsonwebtoken.Claims claims = mock(io.jsonwebtoken.Claims.class);
        when(jwtTokenProvider.parseClaims(validToken)).thenReturn(claims);
        when(claims.get("id", Long.class)).thenReturn(null); // 缺少userId
        when(claims.get("tenantId", Long.class)).thenReturn(1L);
        when(claims.getSubject()).thenReturn("testuser");
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertFalse(result);
        verify(jwtTokenProvider).validateToken(validToken);
        verify(jwtTokenProvider).parseClaims(validToken);
    }

    @Test
    void testTokenParsingException() {
        // 准备测试数据 - token解析异常
        when(handshakeData.getHttpHeaders()).thenReturn(httpHeaders);
        when(httpHeaders.get("Authorization")).thenReturn("Bearer " + validToken);
        when(jwtTokenProvider.validateToken(validToken)).thenReturn(true);
        when(jwtTokenProvider.parseClaims(validToken)).thenThrow(new RuntimeException("Token parsing failed"));
        
        // 执行认证
        boolean result = authInterceptor.isAuthorized(handshakeData);
        
        // 验证结果
        assertFalse(result);
        verify(jwtTokenProvider).validateToken(validToken);
        verify(jwtTokenProvider).parseClaims(validToken);
    }

    @Test
    void testExtractTokenFromAuthHeader() {
        // 测试从Authorization头提取token的各种格式
        SocketJwtAuthInterceptor interceptor = new SocketJwtAuthInterceptor(jwtTokenProvider);
        
        // 使用反射访问私有方法进行测试
        try {
            java.lang.reflect.Method extractTokenMethod = SocketJwtAuthInterceptor.class
                    .getDeclaredMethod("extractToken", String.class);
            extractTokenMethod.setAccessible(true);
            
            // 测试Bearer格式
            String result1 = (String) extractTokenMethod.invoke(interceptor, "Bearer token123");
            assertEquals("token123", result1);
            
            // 测试bearer格式（小写）
            String result2 = (String) extractTokenMethod.invoke(interceptor, "bearer token456");
            assertEquals("token456", result2);
            
            // 测试直接token
            String result3 = (String) extractTokenMethod.invoke(interceptor, "directtoken");
            assertEquals("directtoken", result3);
            
            // 测试null
            String result4 = (String) extractTokenMethod.invoke(interceptor, (String) null);
            assertNull(result4);
            
            // 测试空字符串
            String result5 = (String) extractTokenMethod.invoke(interceptor, "");
            assertNull(result5);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testHelperMethods() {
        // 测试辅助方法
        Map<String, List<String>> urlParams = new HashMap<>();
        urlParams.put("userId", List.of("123"));
        urlParams.put("tenantId", List.of("1"));
        urlParams.put("username", List.of("testuser"));
        urlParams.put("phone", List.of("13800138000"));
        
        when(handshakeData.getSingleUrlParam("userId")).thenReturn("123");
        when(handshakeData.getSingleUrlParam("tenantId")).thenReturn("1");
        when(handshakeData.getSingleUrlParam("username")).thenReturn("testuser");
        when(handshakeData.getSingleUrlParam("phone")).thenReturn("13800138000");
        
        // 测试辅助方法
        assertEquals(Long.valueOf(123), SocketJwtAuthInterceptor.getUserId(handshakeData));
        assertEquals(Long.valueOf(1), SocketJwtAuthInterceptor.getTenantId(handshakeData));
        assertEquals("testuser", SocketJwtAuthInterceptor.getUsername(handshakeData));
        assertEquals("13800138000", SocketJwtAuthInterceptor.getPhone(handshakeData));
    }
}
