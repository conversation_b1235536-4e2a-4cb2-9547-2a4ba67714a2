server:
  port: 18080
#context path
  servlet:
    context-path: /api
spring:
  datasource:
#    url: ***********************************************************************************************************
    url: ********************************************************************************************************
    username: tourism
    password: CeLxp7bF7yTDnYw6
    driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      host: *************
      password:
      database: 10
      port: 6379

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jwt:
  secret: demoSecretKey123456
  expiration: 10080

gewe:
  api:
    baseUrl: http://api.geweapi.com
    appId: wx_Vlj-0pB6Z79HRw6V2YKhR
    token: 9d454e71-9b59-4db8-a5d0-74c7ecaed862

