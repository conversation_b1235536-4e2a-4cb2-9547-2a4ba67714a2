import 'package:flutter/material.dart';
import '../services/gewe_group_service.dart';

class GroupMembersPage extends StatefulWidget {
  final String chatroomId;
  final String? title;
  const GroupMembersPage({super.key, required this.chatroomId, this.title});

  @override
  State<GroupMembersPage> createState() => _GroupMembersPageState();
}

class _GroupMembersPageState extends State<GroupMembersPage> {
  final GeWeGroupService _groupService = GeWeGroupService();
  bool _loading = true;
  String? _error;
  MemberListData? _data;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() {
      _loading = true;
      _error = null;
    });
    final resp = await _groupService.getChatroomMemberList(widget.chatroomId);
    if (!mounted) return;
    if (resp.isSuccess && resp.data != null) {
      setState(() {
        _data = resp.data;
        _loading = false;
      });
    } else {
      setState(() {
        _error = resp.message;
        _loading = false;
      });
    }
  }

  void _openMemberDetail(GroupMember m) {
    Navigator.pushNamed(context, '/group-member-detail', arguments: {
      'chatroomId': widget.chatroomId,
      'memberWxid': m.wxid,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? '群成员'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.redAccent),
            const SizedBox(height: 12),
            Text(_error!),
            const SizedBox(height: 12),
            ElevatedButton(onPressed: _load, child: const Text('重试')),
          ],
        ),
      );
    }
    final list = _data?.memberList ?? [];
    if (list.isEmpty) {
      return const Center(child: Text('暂无成员'));
    }
    return GridView.builder(
      padding: const EdgeInsets.all(12),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: .75,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final m = list[index];
        final display = m.displayName?.isNotEmpty == true
            ? m.displayName!
            : (m.nickName ?? m.wxid);
        return InkWell(
          onTap: () => _openMemberDetail(m),
          child: Column(
            children: [
              CircleAvatar(
                radius: 28,
                backgroundColor: Colors.green[200],
                backgroundImage: (m.smallHeadImgUrl != null && m.smallHeadImgUrl!.isNotEmpty)
                    ? NetworkImage(m.smallHeadImgUrl!)
                    : null,
                child: (m.smallHeadImgUrl == null || m.smallHeadImgUrl!.isEmpty)
                    ? const Icon(Icons.person, color: Colors.white)
                    : null,
              ),
              const SizedBox(height: 8),
              Text(
                display,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      },
    );
  }
}

